(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))r(l);new MutationObserver(l=>{for(const o of l)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(l){const o={};return l.integrity&&(o.integrity=l.integrity),l.referrerPolicy&&(o.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?o.credentials="include":l.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(l){if(l.ep)return;l.ep=!0;const o=n(l);fetch(l.href,o)}})();var as={exports:{}},hl={},cs={exports:{}},M={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ur=Symbol.for("react.element"),jc=Symbol.for("react.portal"),Oc=Symbol.for("react.fragment"),Ic=Symbol.for("react.strict_mode"),Fc=Symbol.for("react.profiler"),Dc=Symbol.for("react.provider"),Ac=Symbol.for("react.context"),Uc=Symbol.for("react.forward_ref"),$c=Symbol.for("react.suspense"),Vc=Symbol.for("react.memo"),Wc=Symbol.for("react.lazy"),Xi=Symbol.iterator;function Bc(e){return e===null||typeof e!="object"?null:(e=Xi&&e[Xi]||e["@@iterator"],typeof e=="function"?e:null)}var fs={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},ds=Object.assign,ps={};function gn(e,t,n){this.props=e,this.context=t,this.refs=ps,this.updater=n||fs}gn.prototype.isReactComponent={};gn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};gn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function ms(){}ms.prototype=gn.prototype;function ei(e,t,n){this.props=e,this.context=t,this.refs=ps,this.updater=n||fs}var ti=ei.prototype=new ms;ti.constructor=ei;ds(ti,gn.prototype);ti.isPureReactComponent=!0;var Zi=Array.isArray,hs=Object.prototype.hasOwnProperty,ni={current:null},vs={key:!0,ref:!0,__self:!0,__source:!0};function gs(e,t,n){var r,l={},o=null,i=null;if(t!=null)for(r in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(o=""+t.key),t)hs.call(t,r)&&!vs.hasOwnProperty(r)&&(l[r]=t[r]);var u=arguments.length-2;if(u===1)l.children=n;else if(1<u){for(var s=Array(u),c=0;c<u;c++)s[c]=arguments[c+2];l.children=s}if(e&&e.defaultProps)for(r in u=e.defaultProps,u)l[r]===void 0&&(l[r]=u[r]);return{$$typeof:ur,type:e,key:o,ref:i,props:l,_owner:ni.current}}function Hc(e,t){return{$$typeof:ur,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ri(e){return typeof e=="object"&&e!==null&&e.$$typeof===ur}function Qc(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Ji=/\/+/g;function jl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Qc(""+e.key):t.toString(36)}function Mr(e,t,n,r,l){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(o){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case ur:case jc:i=!0}}if(i)return i=e,l=l(i),e=r===""?"."+jl(i,0):r,Zi(l)?(n="",e!=null&&(n=e.replace(Ji,"$&/")+"/"),Mr(l,t,n,"",function(c){return c})):l!=null&&(ri(l)&&(l=Hc(l,n+(!l.key||i&&i.key===l.key?"":(""+l.key).replace(Ji,"$&/")+"/")+e)),t.push(l)),1;if(i=0,r=r===""?".":r+":",Zi(e))for(var u=0;u<e.length;u++){o=e[u];var s=r+jl(o,u);i+=Mr(o,t,n,s,l)}else if(s=Bc(e),typeof s=="function")for(e=s.call(e),u=0;!(o=e.next()).done;)o=o.value,s=r+jl(o,u++),i+=Mr(o,t,n,s,l);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function hr(e,t,n){if(e==null)return e;var r=[],l=0;return Mr(e,r,"","",function(o){return t.call(n,o,l++)}),r}function Gc(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var de={current:null},jr={transition:null},Kc={ReactCurrentDispatcher:de,ReactCurrentBatchConfig:jr,ReactCurrentOwner:ni};function ys(){throw Error("act(...) is not supported in production builds of React.")}M.Children={map:hr,forEach:function(e,t,n){hr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return hr(e,function(){t++}),t},toArray:function(e){return hr(e,function(t){return t})||[]},only:function(e){if(!ri(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};M.Component=gn;M.Fragment=Oc;M.Profiler=Fc;M.PureComponent=ei;M.StrictMode=Ic;M.Suspense=$c;M.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Kc;M.act=ys;M.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=ds({},e.props),l=e.key,o=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,i=ni.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(s in t)hs.call(t,s)&&!vs.hasOwnProperty(s)&&(r[s]=t[s]===void 0&&u!==void 0?u[s]:t[s])}var s=arguments.length-2;if(s===1)r.children=n;else if(1<s){u=Array(s);for(var c=0;c<s;c++)u[c]=arguments[c+2];r.children=u}return{$$typeof:ur,type:e.type,key:l,ref:o,props:r,_owner:i}};M.createContext=function(e){return e={$$typeof:Ac,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Dc,_context:e},e.Consumer=e};M.createElement=gs;M.createFactory=function(e){var t=gs.bind(null,e);return t.type=e,t};M.createRef=function(){return{current:null}};M.forwardRef=function(e){return{$$typeof:Uc,render:e}};M.isValidElement=ri;M.lazy=function(e){return{$$typeof:Wc,_payload:{_status:-1,_result:e},_init:Gc}};M.memo=function(e,t){return{$$typeof:Vc,type:e,compare:t===void 0?null:t}};M.startTransition=function(e){var t=jr.transition;jr.transition={};try{e()}finally{jr.transition=t}};M.unstable_act=ys;M.useCallback=function(e,t){return de.current.useCallback(e,t)};M.useContext=function(e){return de.current.useContext(e)};M.useDebugValue=function(){};M.useDeferredValue=function(e){return de.current.useDeferredValue(e)};M.useEffect=function(e,t){return de.current.useEffect(e,t)};M.useId=function(){return de.current.useId()};M.useImperativeHandle=function(e,t,n){return de.current.useImperativeHandle(e,t,n)};M.useInsertionEffect=function(e,t){return de.current.useInsertionEffect(e,t)};M.useLayoutEffect=function(e,t){return de.current.useLayoutEffect(e,t)};M.useMemo=function(e,t){return de.current.useMemo(e,t)};M.useReducer=function(e,t,n){return de.current.useReducer(e,t,n)};M.useRef=function(e){return de.current.useRef(e)};M.useState=function(e){return de.current.useState(e)};M.useSyncExternalStore=function(e,t,n){return de.current.useSyncExternalStore(e,t,n)};M.useTransition=function(){return de.current.useTransition()};M.version="18.3.1";cs.exports=M;var j=cs.exports;/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Yc=j,Xc=Symbol.for("react.element"),Zc=Symbol.for("react.fragment"),Jc=Object.prototype.hasOwnProperty,qc=Yc.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,bc={key:!0,ref:!0,__self:!0,__source:!0};function ws(e,t,n){var r,l={},o=null,i=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)Jc.call(t,r)&&!bc.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)l[r]===void 0&&(l[r]=t[r]);return{$$typeof:Xc,type:e,key:o,ref:i,props:l,_owner:qc.current}}hl.Fragment=Zc;hl.jsx=ws;hl.jsxs=ws;as.exports=hl;var D=as.exports,ks={exports:{}},Ce={},Ss={exports:{}},xs={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(x,P){var T=x.length;x.push(P);e:for(;0<T;){var I=T-1>>>1,b=x[I];if(0<l(b,P))x[I]=P,x[T]=b,T=I;else break e}}function n(x){return x.length===0?null:x[0]}function r(x){if(x.length===0)return null;var P=x[0],T=x.pop();if(T!==P){x[0]=T;e:for(var I=0,b=x.length,pr=b>>>1;I<pr;){var Nt=2*(I+1)-1,Ml=x[Nt],zt=Nt+1,mr=x[zt];if(0>l(Ml,T))zt<b&&0>l(mr,Ml)?(x[I]=mr,x[zt]=T,I=zt):(x[I]=Ml,x[Nt]=T,I=Nt);else if(zt<b&&0>l(mr,T))x[I]=mr,x[zt]=T,I=zt;else break e}}return P}function l(x,P){var T=x.sortIndex-P.sortIndex;return T!==0?T:x.id-P.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var i=Date,u=i.now();e.unstable_now=function(){return i.now()-u}}var s=[],c=[],h=1,m=null,p=3,y=!1,k=!1,w=!1,z=typeof setTimeout=="function"?setTimeout:null,f=typeof clearTimeout=="function"?clearTimeout:null,a=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function d(x){for(var P=n(c);P!==null;){if(P.callback===null)r(c);else if(P.startTime<=x)r(c),P.sortIndex=P.expirationTime,t(s,P);else break;P=n(c)}}function v(x){if(w=!1,d(x),!k)if(n(s)!==null)k=!0,tt(S);else{var P=n(c);P!==null&&Wt(v,P.startTime-x)}}function S(x,P){k=!1,w&&(w=!1,f(N),N=-1),y=!0;var T=p;try{for(d(P),m=n(s);m!==null&&(!(m.expirationTime>P)||x&&!ae());){var I=m.callback;if(typeof I=="function"){m.callback=null,p=m.priorityLevel;var b=I(m.expirationTime<=P);P=e.unstable_now(),typeof b=="function"?m.callback=b:m===n(s)&&r(s),d(P)}else r(s);m=n(s)}if(m!==null)var pr=!0;else{var Nt=n(c);Nt!==null&&Wt(v,Nt.startTime-P),pr=!1}return pr}finally{m=null,p=T,y=!1}}var E=!1,_=null,N=-1,$=5,R=-1;function ae(){return!(e.unstable_now()-R<$)}function F(){if(_!==null){var x=e.unstable_now();R=x;var P=!0;try{P=_(!0,x)}finally{P?et():(E=!1,_=null)}}else E=!1}var et;if(typeof a=="function")et=function(){a(F)};else if(typeof MessageChannel<"u"){var _t=new MessageChannel,dr=_t.port2;_t.port1.onmessage=F,et=function(){dr.postMessage(null)}}else et=function(){z(F,0)};function tt(x){_=x,E||(E=!0,et())}function Wt(x,P){N=z(function(){x(e.unstable_now())},P)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(x){x.callback=null},e.unstable_continueExecution=function(){k||y||(k=!0,tt(S))},e.unstable_forceFrameRate=function(x){0>x||125<x?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):$=0<x?Math.floor(1e3/x):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(s)},e.unstable_next=function(x){switch(p){case 1:case 2:case 3:var P=3;break;default:P=p}var T=p;p=P;try{return x()}finally{p=T}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(x,P){switch(x){case 1:case 2:case 3:case 4:case 5:break;default:x=3}var T=p;p=x;try{return P()}finally{p=T}},e.unstable_scheduleCallback=function(x,P,T){var I=e.unstable_now();switch(typeof T=="object"&&T!==null?(T=T.delay,T=typeof T=="number"&&0<T?I+T:I):T=I,x){case 1:var b=-1;break;case 2:b=250;break;case 5:b=**********;break;case 4:b=1e4;break;default:b=5e3}return b=T+b,x={id:h++,callback:P,priorityLevel:x,startTime:T,expirationTime:b,sortIndex:-1},T>I?(x.sortIndex=T,t(c,x),n(s)===null&&x===n(c)&&(w?(f(N),N=-1):w=!0,Wt(v,T-I))):(x.sortIndex=b,t(s,x),k||y||(k=!0,tt(S))),x},e.unstable_shouldYield=ae,e.unstable_wrapCallback=function(x){var P=p;return function(){var T=p;p=P;try{return x.apply(this,arguments)}finally{p=T}}}})(xs);Ss.exports=xs;var ef=Ss.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var tf=j,xe=ef;function g(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Cs=new Set,Hn={};function $t(e,t){cn(e,t),cn(e+"Capture",t)}function cn(e,t){for(Hn[e]=t,e=0;e<t.length;e++)Cs.add(t[e])}var Xe=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),io=Object.prototype.hasOwnProperty,nf=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,qi={},bi={};function rf(e){return io.call(bi,e)?!0:io.call(qi,e)?!1:nf.test(e)?bi[e]=!0:(qi[e]=!0,!1)}function lf(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function of(e,t,n,r){if(t===null||typeof t>"u"||lf(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function pe(e,t,n,r,l,o,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var le={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){le[e]=new pe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];le[t]=new pe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){le[e]=new pe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){le[e]=new pe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){le[e]=new pe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){le[e]=new pe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){le[e]=new pe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){le[e]=new pe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){le[e]=new pe(e,5,!1,e.toLowerCase(),null,!1,!1)});var li=/[\-:]([a-z])/g;function oi(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(li,oi);le[t]=new pe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(li,oi);le[t]=new pe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(li,oi);le[t]=new pe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){le[e]=new pe(e,1,!1,e.toLowerCase(),null,!1,!1)});le.xlinkHref=new pe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){le[e]=new pe(e,1,!1,e.toLowerCase(),null,!0,!0)});function ii(e,t,n,r){var l=le.hasOwnProperty(t)?le[t]:null;(l!==null?l.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(of(t,n,l,r)&&(n=null),r||l===null?rf(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,r=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var be=tf.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,vr=Symbol.for("react.element"),Ht=Symbol.for("react.portal"),Qt=Symbol.for("react.fragment"),ui=Symbol.for("react.strict_mode"),uo=Symbol.for("react.profiler"),Es=Symbol.for("react.provider"),_s=Symbol.for("react.context"),si=Symbol.for("react.forward_ref"),so=Symbol.for("react.suspense"),ao=Symbol.for("react.suspense_list"),ai=Symbol.for("react.memo"),ot=Symbol.for("react.lazy"),Ns=Symbol.for("react.offscreen"),eu=Symbol.iterator;function Sn(e){return e===null||typeof e!="object"?null:(e=eu&&e[eu]||e["@@iterator"],typeof e=="function"?e:null)}var K=Object.assign,Ol;function Rn(e){if(Ol===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ol=t&&t[1]||""}return`
`+Ol+e}var Il=!1;function Fl(e,t){if(!e||Il)return"";Il=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var l=c.stack.split(`
`),o=r.stack.split(`
`),i=l.length-1,u=o.length-1;1<=i&&0<=u&&l[i]!==o[u];)u--;for(;1<=i&&0<=u;i--,u--)if(l[i]!==o[u]){if(i!==1||u!==1)do if(i--,u--,0>u||l[i]!==o[u]){var s=`
`+l[i].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}while(1<=i&&0<=u);break}}}finally{Il=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Rn(e):""}function uf(e){switch(e.tag){case 5:return Rn(e.type);case 16:return Rn("Lazy");case 13:return Rn("Suspense");case 19:return Rn("SuspenseList");case 0:case 2:case 15:return e=Fl(e.type,!1),e;case 11:return e=Fl(e.type.render,!1),e;case 1:return e=Fl(e.type,!0),e;default:return""}}function co(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Qt:return"Fragment";case Ht:return"Portal";case uo:return"Profiler";case ui:return"StrictMode";case so:return"Suspense";case ao:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case _s:return(e.displayName||"Context")+".Consumer";case Es:return(e._context.displayName||"Context")+".Provider";case si:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ai:return t=e.displayName||null,t!==null?t:co(e.type)||"Memo";case ot:t=e._payload,e=e._init;try{return co(e(t))}catch{}}return null}function sf(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return co(t);case 8:return t===ui?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function wt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function zs(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function af(e){var t=zs(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(i){r=""+i,o.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(i){r=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function gr(e){e._valueTracker||(e._valueTracker=af(e))}function Ps(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=zs(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Qr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function fo(e,t){var n=t.checked;return K({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function tu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=wt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Ts(e,t){t=t.checked,t!=null&&ii(e,"checked",t,!1)}function po(e,t){Ts(e,t);var n=wt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?mo(e,t.type,n):t.hasOwnProperty("defaultValue")&&mo(e,t.type,wt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function nu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function mo(e,t,n){(t!=="number"||Qr(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Mn=Array.isArray;function nn(e,t,n,r){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&r&&(e[n].defaultSelected=!0)}else{for(n=""+wt(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,r&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function ho(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(g(91));return K({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ru(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(g(92));if(Mn(n)){if(1<n.length)throw Error(g(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:wt(n)}}function Ls(e,t){var n=wt(t.value),r=wt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function lu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Rs(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function vo(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Rs(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var yr,Ms=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(yr=yr||document.createElement("div"),yr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=yr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Qn(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var In={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},cf=["Webkit","ms","Moz","O"];Object.keys(In).forEach(function(e){cf.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),In[t]=In[e]})});function js(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||In.hasOwnProperty(e)&&In[e]?(""+t).trim():t+"px"}function Os(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,l=js(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,l):e[n]=l}}var ff=K({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function go(e,t){if(t){if(ff[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(g(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(g(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(g(61))}if(t.style!=null&&typeof t.style!="object")throw Error(g(62))}}function yo(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var wo=null;function ci(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ko=null,rn=null,ln=null;function ou(e){if(e=cr(e)){if(typeof ko!="function")throw Error(g(280));var t=e.stateNode;t&&(t=kl(t),ko(e.stateNode,e.type,t))}}function Is(e){rn?ln?ln.push(e):ln=[e]:rn=e}function Fs(){if(rn){var e=rn,t=ln;if(ln=rn=null,ou(e),t)for(e=0;e<t.length;e++)ou(t[e])}}function Ds(e,t){return e(t)}function As(){}var Dl=!1;function Us(e,t,n){if(Dl)return e(t,n);Dl=!0;try{return Ds(e,t,n)}finally{Dl=!1,(rn!==null||ln!==null)&&(As(),Fs())}}function Gn(e,t){var n=e.stateNode;if(n===null)return null;var r=kl(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(g(231,t,typeof n));return n}var So=!1;if(Xe)try{var xn={};Object.defineProperty(xn,"passive",{get:function(){So=!0}}),window.addEventListener("test",xn,xn),window.removeEventListener("test",xn,xn)}catch{So=!1}function df(e,t,n,r,l,o,i,u,s){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(h){this.onError(h)}}var Fn=!1,Gr=null,Kr=!1,xo=null,pf={onError:function(e){Fn=!0,Gr=e}};function mf(e,t,n,r,l,o,i,u,s){Fn=!1,Gr=null,df.apply(pf,arguments)}function hf(e,t,n,r,l,o,i,u,s){if(mf.apply(this,arguments),Fn){if(Fn){var c=Gr;Fn=!1,Gr=null}else throw Error(g(198));Kr||(Kr=!0,xo=c)}}function Vt(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function $s(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function iu(e){if(Vt(e)!==e)throw Error(g(188))}function vf(e){var t=e.alternate;if(!t){if(t=Vt(e),t===null)throw Error(g(188));return t!==e?null:e}for(var n=e,r=t;;){var l=n.return;if(l===null)break;var o=l.alternate;if(o===null){if(r=l.return,r!==null){n=r;continue}break}if(l.child===o.child){for(o=l.child;o;){if(o===n)return iu(l),e;if(o===r)return iu(l),t;o=o.sibling}throw Error(g(188))}if(n.return!==r.return)n=l,r=o;else{for(var i=!1,u=l.child;u;){if(u===n){i=!0,n=l,r=o;break}if(u===r){i=!0,r=l,n=o;break}u=u.sibling}if(!i){for(u=o.child;u;){if(u===n){i=!0,n=o,r=l;break}if(u===r){i=!0,r=o,n=l;break}u=u.sibling}if(!i)throw Error(g(189))}}if(n.alternate!==r)throw Error(g(190))}if(n.tag!==3)throw Error(g(188));return n.stateNode.current===n?e:t}function Vs(e){return e=vf(e),e!==null?Ws(e):null}function Ws(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Ws(e);if(t!==null)return t;e=e.sibling}return null}var Bs=xe.unstable_scheduleCallback,uu=xe.unstable_cancelCallback,gf=xe.unstable_shouldYield,yf=xe.unstable_requestPaint,X=xe.unstable_now,wf=xe.unstable_getCurrentPriorityLevel,fi=xe.unstable_ImmediatePriority,Hs=xe.unstable_UserBlockingPriority,Yr=xe.unstable_NormalPriority,kf=xe.unstable_LowPriority,Qs=xe.unstable_IdlePriority,vl=null,Ve=null;function Sf(e){if(Ve&&typeof Ve.onCommitFiberRoot=="function")try{Ve.onCommitFiberRoot(vl,e,void 0,(e.current.flags&128)===128)}catch{}}var Ie=Math.clz32?Math.clz32:Ef,xf=Math.log,Cf=Math.LN2;function Ef(e){return e>>>=0,e===0?32:31-(xf(e)/Cf|0)|0}var wr=64,kr=4194304;function jn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Xr(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,l=e.suspendedLanes,o=e.pingedLanes,i=n&268435455;if(i!==0){var u=i&~l;u!==0?r=jn(u):(o&=i,o!==0&&(r=jn(o)))}else i=n&~l,i!==0?r=jn(i):o!==0&&(r=jn(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&l)&&(l=r&-r,o=t&-t,l>=o||l===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ie(t),l=1<<n,r|=e[n],t&=~l;return r}function _f(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Nf(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,l=e.expirationTimes,o=e.pendingLanes;0<o;){var i=31-Ie(o),u=1<<i,s=l[i];s===-1?(!(u&n)||u&r)&&(l[i]=_f(u,t)):s<=t&&(e.expiredLanes|=u),o&=~u}}function Co(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Gs(){var e=wr;return wr<<=1,!(wr&4194240)&&(wr=64),e}function Al(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function sr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ie(t),e[t]=n}function zf(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-Ie(n),o=1<<l;t[l]=0,r[l]=-1,e[l]=-1,n&=~o}}function di(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ie(n),l=1<<r;l&t|e[r]&t&&(e[r]|=t),n&=~l}}var A=0;function Ks(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Ys,pi,Xs,Zs,Js,Eo=!1,Sr=[],ft=null,dt=null,pt=null,Kn=new Map,Yn=new Map,ut=[],Pf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function su(e,t){switch(e){case"focusin":case"focusout":ft=null;break;case"dragenter":case"dragleave":dt=null;break;case"mouseover":case"mouseout":pt=null;break;case"pointerover":case"pointerout":Kn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Yn.delete(t.pointerId)}}function Cn(e,t,n,r,l,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[l]},t!==null&&(t=cr(t),t!==null&&pi(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function Tf(e,t,n,r,l){switch(t){case"focusin":return ft=Cn(ft,e,t,n,r,l),!0;case"dragenter":return dt=Cn(dt,e,t,n,r,l),!0;case"mouseover":return pt=Cn(pt,e,t,n,r,l),!0;case"pointerover":var o=l.pointerId;return Kn.set(o,Cn(Kn.get(o)||null,e,t,n,r,l)),!0;case"gotpointercapture":return o=l.pointerId,Yn.set(o,Cn(Yn.get(o)||null,e,t,n,r,l)),!0}return!1}function qs(e){var t=Lt(e.target);if(t!==null){var n=Vt(t);if(n!==null){if(t=n.tag,t===13){if(t=$s(n),t!==null){e.blockedOn=t,Js(e.priority,function(){Xs(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Or(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=_o(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);wo=r,n.target.dispatchEvent(r),wo=null}else return t=cr(n),t!==null&&pi(t),e.blockedOn=n,!1;t.shift()}return!0}function au(e,t,n){Or(e)&&n.delete(t)}function Lf(){Eo=!1,ft!==null&&Or(ft)&&(ft=null),dt!==null&&Or(dt)&&(dt=null),pt!==null&&Or(pt)&&(pt=null),Kn.forEach(au),Yn.forEach(au)}function En(e,t){e.blockedOn===t&&(e.blockedOn=null,Eo||(Eo=!0,xe.unstable_scheduleCallback(xe.unstable_NormalPriority,Lf)))}function Xn(e){function t(l){return En(l,e)}if(0<Sr.length){En(Sr[0],e);for(var n=1;n<Sr.length;n++){var r=Sr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(ft!==null&&En(ft,e),dt!==null&&En(dt,e),pt!==null&&En(pt,e),Kn.forEach(t),Yn.forEach(t),n=0;n<ut.length;n++)r=ut[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<ut.length&&(n=ut[0],n.blockedOn===null);)qs(n),n.blockedOn===null&&ut.shift()}var on=be.ReactCurrentBatchConfig,Zr=!0;function Rf(e,t,n,r){var l=A,o=on.transition;on.transition=null;try{A=1,mi(e,t,n,r)}finally{A=l,on.transition=o}}function Mf(e,t,n,r){var l=A,o=on.transition;on.transition=null;try{A=4,mi(e,t,n,r)}finally{A=l,on.transition=o}}function mi(e,t,n,r){if(Zr){var l=_o(e,t,n,r);if(l===null)Yl(e,t,r,Jr,n),su(e,r);else if(Tf(l,e,t,n,r))r.stopPropagation();else if(su(e,r),t&4&&-1<Pf.indexOf(e)){for(;l!==null;){var o=cr(l);if(o!==null&&Ys(o),o=_o(e,t,n,r),o===null&&Yl(e,t,r,Jr,n),o===l)break;l=o}l!==null&&r.stopPropagation()}else Yl(e,t,r,null,n)}}var Jr=null;function _o(e,t,n,r){if(Jr=null,e=ci(r),e=Lt(e),e!==null)if(t=Vt(e),t===null)e=null;else if(n=t.tag,n===13){if(e=$s(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Jr=e,null}function bs(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(wf()){case fi:return 1;case Hs:return 4;case Yr:case kf:return 16;case Qs:return 536870912;default:return 16}default:return 16}}var at=null,hi=null,Ir=null;function ea(){if(Ir)return Ir;var e,t=hi,n=t.length,r,l="value"in at?at.value:at.textContent,o=l.length;for(e=0;e<n&&t[e]===l[e];e++);var i=n-e;for(r=1;r<=i&&t[n-r]===l[o-r];r++);return Ir=l.slice(e,1<r?1-r:void 0)}function Fr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function xr(){return!0}function cu(){return!1}function Ee(e){function t(n,r,l,o,i){this._reactName=n,this._targetInst=l,this.type=r,this.nativeEvent=o,this.target=i,this.currentTarget=null;for(var u in e)e.hasOwnProperty(u)&&(n=e[u],this[u]=n?n(o):o[u]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?xr:cu,this.isPropagationStopped=cu,this}return K(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=xr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=xr)},persist:function(){},isPersistent:xr}),t}var yn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},vi=Ee(yn),ar=K({},yn,{view:0,detail:0}),jf=Ee(ar),Ul,$l,_n,gl=K({},ar,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:gi,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==_n&&(_n&&e.type==="mousemove"?(Ul=e.screenX-_n.screenX,$l=e.screenY-_n.screenY):$l=Ul=0,_n=e),Ul)},movementY:function(e){return"movementY"in e?e.movementY:$l}}),fu=Ee(gl),Of=K({},gl,{dataTransfer:0}),If=Ee(Of),Ff=K({},ar,{relatedTarget:0}),Vl=Ee(Ff),Df=K({},yn,{animationName:0,elapsedTime:0,pseudoElement:0}),Af=Ee(Df),Uf=K({},yn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),$f=Ee(Uf),Vf=K({},yn,{data:0}),du=Ee(Vf),Wf={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Bf={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Hf={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Qf(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Hf[e])?!!t[e]:!1}function gi(){return Qf}var Gf=K({},ar,{key:function(e){if(e.key){var t=Wf[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Fr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Bf[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:gi,charCode:function(e){return e.type==="keypress"?Fr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Fr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Kf=Ee(Gf),Yf=K({},gl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),pu=Ee(Yf),Xf=K({},ar,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:gi}),Zf=Ee(Xf),Jf=K({},yn,{propertyName:0,elapsedTime:0,pseudoElement:0}),qf=Ee(Jf),bf=K({},gl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ed=Ee(bf),td=[9,13,27,32],yi=Xe&&"CompositionEvent"in window,Dn=null;Xe&&"documentMode"in document&&(Dn=document.documentMode);var nd=Xe&&"TextEvent"in window&&!Dn,ta=Xe&&(!yi||Dn&&8<Dn&&11>=Dn),mu=" ",hu=!1;function na(e,t){switch(e){case"keyup":return td.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ra(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Gt=!1;function rd(e,t){switch(e){case"compositionend":return ra(t);case"keypress":return t.which!==32?null:(hu=!0,mu);case"textInput":return e=t.data,e===mu&&hu?null:e;default:return null}}function ld(e,t){if(Gt)return e==="compositionend"||!yi&&na(e,t)?(e=ea(),Ir=hi=at=null,Gt=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return ta&&t.locale!=="ko"?null:t.data;default:return null}}var od={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function vu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!od[e.type]:t==="textarea"}function la(e,t,n,r){Is(r),t=qr(t,"onChange"),0<t.length&&(n=new vi("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var An=null,Zn=null;function id(e){ha(e,0)}function yl(e){var t=Xt(e);if(Ps(t))return e}function ud(e,t){if(e==="change")return t}var oa=!1;if(Xe){var Wl;if(Xe){var Bl="oninput"in document;if(!Bl){var gu=document.createElement("div");gu.setAttribute("oninput","return;"),Bl=typeof gu.oninput=="function"}Wl=Bl}else Wl=!1;oa=Wl&&(!document.documentMode||9<document.documentMode)}function yu(){An&&(An.detachEvent("onpropertychange",ia),Zn=An=null)}function ia(e){if(e.propertyName==="value"&&yl(Zn)){var t=[];la(t,Zn,e,ci(e)),Us(id,t)}}function sd(e,t,n){e==="focusin"?(yu(),An=t,Zn=n,An.attachEvent("onpropertychange",ia)):e==="focusout"&&yu()}function ad(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return yl(Zn)}function cd(e,t){if(e==="click")return yl(t)}function fd(e,t){if(e==="input"||e==="change")return yl(t)}function dd(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var De=typeof Object.is=="function"?Object.is:dd;function Jn(e,t){if(De(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var l=n[r];if(!io.call(t,l)||!De(e[l],t[l]))return!1}return!0}function wu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ku(e,t){var n=wu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=wu(n)}}function ua(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?ua(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function sa(){for(var e=window,t=Qr();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Qr(e.document)}return t}function wi(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function pd(e){var t=sa(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&ua(n.ownerDocument.documentElement,n)){if(r!==null&&wi(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,o=Math.min(r.start,l);r=r.end===void 0?o:Math.min(r.end,l),!e.extend&&o>r&&(l=r,r=o,o=l),l=ku(n,o);var i=ku(n,r);l&&i&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var md=Xe&&"documentMode"in document&&11>=document.documentMode,Kt=null,No=null,Un=null,zo=!1;function Su(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;zo||Kt==null||Kt!==Qr(r)||(r=Kt,"selectionStart"in r&&wi(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Un&&Jn(Un,r)||(Un=r,r=qr(No,"onSelect"),0<r.length&&(t=new vi("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Kt)))}function Cr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Yt={animationend:Cr("Animation","AnimationEnd"),animationiteration:Cr("Animation","AnimationIteration"),animationstart:Cr("Animation","AnimationStart"),transitionend:Cr("Transition","TransitionEnd")},Hl={},aa={};Xe&&(aa=document.createElement("div").style,"AnimationEvent"in window||(delete Yt.animationend.animation,delete Yt.animationiteration.animation,delete Yt.animationstart.animation),"TransitionEvent"in window||delete Yt.transitionend.transition);function wl(e){if(Hl[e])return Hl[e];if(!Yt[e])return e;var t=Yt[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in aa)return Hl[e]=t[n];return e}var ca=wl("animationend"),fa=wl("animationiteration"),da=wl("animationstart"),pa=wl("transitionend"),ma=new Map,xu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function St(e,t){ma.set(e,t),$t(t,[e])}for(var Ql=0;Ql<xu.length;Ql++){var Gl=xu[Ql],hd=Gl.toLowerCase(),vd=Gl[0].toUpperCase()+Gl.slice(1);St(hd,"on"+vd)}St(ca,"onAnimationEnd");St(fa,"onAnimationIteration");St(da,"onAnimationStart");St("dblclick","onDoubleClick");St("focusin","onFocus");St("focusout","onBlur");St(pa,"onTransitionEnd");cn("onMouseEnter",["mouseout","mouseover"]);cn("onMouseLeave",["mouseout","mouseover"]);cn("onPointerEnter",["pointerout","pointerover"]);cn("onPointerLeave",["pointerout","pointerover"]);$t("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));$t("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));$t("onBeforeInput",["compositionend","keypress","textInput","paste"]);$t("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));$t("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));$t("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var On="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),gd=new Set("cancel close invalid load scroll toggle".split(" ").concat(On));function Cu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,hf(r,t,void 0,e),e.currentTarget=null}function ha(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],l=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var i=r.length-1;0<=i;i--){var u=r[i],s=u.instance,c=u.currentTarget;if(u=u.listener,s!==o&&l.isPropagationStopped())break e;Cu(l,u,c),o=s}else for(i=0;i<r.length;i++){if(u=r[i],s=u.instance,c=u.currentTarget,u=u.listener,s!==o&&l.isPropagationStopped())break e;Cu(l,u,c),o=s}}}if(Kr)throw e=xo,Kr=!1,xo=null,e}function W(e,t){var n=t[Mo];n===void 0&&(n=t[Mo]=new Set);var r=e+"__bubble";n.has(r)||(va(t,e,2,!1),n.add(r))}function Kl(e,t,n){var r=0;t&&(r|=4),va(n,e,r,t)}var Er="_reactListening"+Math.random().toString(36).slice(2);function qn(e){if(!e[Er]){e[Er]=!0,Cs.forEach(function(n){n!=="selectionchange"&&(gd.has(n)||Kl(n,!1,e),Kl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Er]||(t[Er]=!0,Kl("selectionchange",!1,t))}}function va(e,t,n,r){switch(bs(t)){case 1:var l=Rf;break;case 4:l=Mf;break;default:l=mi}n=l.bind(null,t,n,e),l=void 0,!So||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),r?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function Yl(e,t,n,r,l){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var i=r.tag;if(i===3||i===4){var u=r.stateNode.containerInfo;if(u===l||u.nodeType===8&&u.parentNode===l)break;if(i===4)for(i=r.return;i!==null;){var s=i.tag;if((s===3||s===4)&&(s=i.stateNode.containerInfo,s===l||s.nodeType===8&&s.parentNode===l))return;i=i.return}for(;u!==null;){if(i=Lt(u),i===null)return;if(s=i.tag,s===5||s===6){r=o=i;continue e}u=u.parentNode}}r=r.return}Us(function(){var c=o,h=ci(n),m=[];e:{var p=ma.get(e);if(p!==void 0){var y=vi,k=e;switch(e){case"keypress":if(Fr(n)===0)break e;case"keydown":case"keyup":y=Kf;break;case"focusin":k="focus",y=Vl;break;case"focusout":k="blur",y=Vl;break;case"beforeblur":case"afterblur":y=Vl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=fu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=If;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=Zf;break;case ca:case fa:case da:y=Af;break;case pa:y=qf;break;case"scroll":y=jf;break;case"wheel":y=ed;break;case"copy":case"cut":case"paste":y=$f;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=pu}var w=(t&4)!==0,z=!w&&e==="scroll",f=w?p!==null?p+"Capture":null:p;w=[];for(var a=c,d;a!==null;){d=a;var v=d.stateNode;if(d.tag===5&&v!==null&&(d=v,f!==null&&(v=Gn(a,f),v!=null&&w.push(bn(a,v,d)))),z)break;a=a.return}0<w.length&&(p=new y(p,k,null,n,h),m.push({event:p,listeners:w}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",p&&n!==wo&&(k=n.relatedTarget||n.fromElement)&&(Lt(k)||k[Ze]))break e;if((y||p)&&(p=h.window===h?h:(p=h.ownerDocument)?p.defaultView||p.parentWindow:window,y?(k=n.relatedTarget||n.toElement,y=c,k=k?Lt(k):null,k!==null&&(z=Vt(k),k!==z||k.tag!==5&&k.tag!==6)&&(k=null)):(y=null,k=c),y!==k)){if(w=fu,v="onMouseLeave",f="onMouseEnter",a="mouse",(e==="pointerout"||e==="pointerover")&&(w=pu,v="onPointerLeave",f="onPointerEnter",a="pointer"),z=y==null?p:Xt(y),d=k==null?p:Xt(k),p=new w(v,a+"leave",y,n,h),p.target=z,p.relatedTarget=d,v=null,Lt(h)===c&&(w=new w(f,a+"enter",k,n,h),w.target=d,w.relatedTarget=z,v=w),z=v,y&&k)t:{for(w=y,f=k,a=0,d=w;d;d=Bt(d))a++;for(d=0,v=f;v;v=Bt(v))d++;for(;0<a-d;)w=Bt(w),a--;for(;0<d-a;)f=Bt(f),d--;for(;a--;){if(w===f||f!==null&&w===f.alternate)break t;w=Bt(w),f=Bt(f)}w=null}else w=null;y!==null&&Eu(m,p,y,w,!1),k!==null&&z!==null&&Eu(m,z,k,w,!0)}}e:{if(p=c?Xt(c):window,y=p.nodeName&&p.nodeName.toLowerCase(),y==="select"||y==="input"&&p.type==="file")var S=ud;else if(vu(p))if(oa)S=fd;else{S=ad;var E=sd}else(y=p.nodeName)&&y.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(S=cd);if(S&&(S=S(e,c))){la(m,S,n,h);break e}E&&E(e,p,c),e==="focusout"&&(E=p._wrapperState)&&E.controlled&&p.type==="number"&&mo(p,"number",p.value)}switch(E=c?Xt(c):window,e){case"focusin":(vu(E)||E.contentEditable==="true")&&(Kt=E,No=c,Un=null);break;case"focusout":Un=No=Kt=null;break;case"mousedown":zo=!0;break;case"contextmenu":case"mouseup":case"dragend":zo=!1,Su(m,n,h);break;case"selectionchange":if(md)break;case"keydown":case"keyup":Su(m,n,h)}var _;if(yi)e:{switch(e){case"compositionstart":var N="onCompositionStart";break e;case"compositionend":N="onCompositionEnd";break e;case"compositionupdate":N="onCompositionUpdate";break e}N=void 0}else Gt?na(e,n)&&(N="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(N="onCompositionStart");N&&(ta&&n.locale!=="ko"&&(Gt||N!=="onCompositionStart"?N==="onCompositionEnd"&&Gt&&(_=ea()):(at=h,hi="value"in at?at.value:at.textContent,Gt=!0)),E=qr(c,N),0<E.length&&(N=new du(N,e,null,n,h),m.push({event:N,listeners:E}),_?N.data=_:(_=ra(n),_!==null&&(N.data=_)))),(_=nd?rd(e,n):ld(e,n))&&(c=qr(c,"onBeforeInput"),0<c.length&&(h=new du("onBeforeInput","beforeinput",null,n,h),m.push({event:h,listeners:c}),h.data=_))}ha(m,t)})}function bn(e,t,n){return{instance:e,listener:t,currentTarget:n}}function qr(e,t){for(var n=t+"Capture",r=[];e!==null;){var l=e,o=l.stateNode;l.tag===5&&o!==null&&(l=o,o=Gn(e,n),o!=null&&r.unshift(bn(e,o,l)),o=Gn(e,t),o!=null&&r.push(bn(e,o,l))),e=e.return}return r}function Bt(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Eu(e,t,n,r,l){for(var o=t._reactName,i=[];n!==null&&n!==r;){var u=n,s=u.alternate,c=u.stateNode;if(s!==null&&s===r)break;u.tag===5&&c!==null&&(u=c,l?(s=Gn(n,o),s!=null&&i.unshift(bn(n,s,u))):l||(s=Gn(n,o),s!=null&&i.push(bn(n,s,u)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var yd=/\r\n?/g,wd=/\u0000|\uFFFD/g;function _u(e){return(typeof e=="string"?e:""+e).replace(yd,`
`).replace(wd,"")}function _r(e,t,n){if(t=_u(t),_u(e)!==t&&n)throw Error(g(425))}function br(){}var Po=null,To=null;function Lo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ro=typeof setTimeout=="function"?setTimeout:void 0,kd=typeof clearTimeout=="function"?clearTimeout:void 0,Nu=typeof Promise=="function"?Promise:void 0,Sd=typeof queueMicrotask=="function"?queueMicrotask:typeof Nu<"u"?function(e){return Nu.resolve(null).then(e).catch(xd)}:Ro;function xd(e){setTimeout(function(){throw e})}function Xl(e,t){var n=t,r=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(r===0){e.removeChild(l),Xn(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=l}while(n);Xn(t)}function mt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function zu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var wn=Math.random().toString(36).slice(2),$e="__reactFiber$"+wn,er="__reactProps$"+wn,Ze="__reactContainer$"+wn,Mo="__reactEvents$"+wn,Cd="__reactListeners$"+wn,Ed="__reactHandles$"+wn;function Lt(e){var t=e[$e];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ze]||n[$e]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=zu(e);e!==null;){if(n=e[$e])return n;e=zu(e)}return t}e=n,n=e.parentNode}return null}function cr(e){return e=e[$e]||e[Ze],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Xt(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(g(33))}function kl(e){return e[er]||null}var jo=[],Zt=-1;function xt(e){return{current:e}}function B(e){0>Zt||(e.current=jo[Zt],jo[Zt]=null,Zt--)}function U(e,t){Zt++,jo[Zt]=e.current,e.current=t}var kt={},se=xt(kt),ve=xt(!1),It=kt;function fn(e,t){var n=e.type.contextTypes;if(!n)return kt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var l={},o;for(o in n)l[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function ge(e){return e=e.childContextTypes,e!=null}function el(){B(ve),B(se)}function Pu(e,t,n){if(se.current!==kt)throw Error(g(168));U(se,t),U(ve,n)}function ga(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var l in r)if(!(l in t))throw Error(g(108,sf(e)||"Unknown",l));return K({},n,r)}function tl(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||kt,It=se.current,U(se,e),U(ve,ve.current),!0}function Tu(e,t,n){var r=e.stateNode;if(!r)throw Error(g(169));n?(e=ga(e,t,It),r.__reactInternalMemoizedMergedChildContext=e,B(ve),B(se),U(se,e)):B(ve),U(ve,n)}var Qe=null,Sl=!1,Zl=!1;function ya(e){Qe===null?Qe=[e]:Qe.push(e)}function _d(e){Sl=!0,ya(e)}function Ct(){if(!Zl&&Qe!==null){Zl=!0;var e=0,t=A;try{var n=Qe;for(A=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Qe=null,Sl=!1}catch(l){throw Qe!==null&&(Qe=Qe.slice(e+1)),Bs(fi,Ct),l}finally{A=t,Zl=!1}}return null}var Jt=[],qt=0,nl=null,rl=0,_e=[],Ne=0,Ft=null,Ge=1,Ke="";function Pt(e,t){Jt[qt++]=rl,Jt[qt++]=nl,nl=e,rl=t}function wa(e,t,n){_e[Ne++]=Ge,_e[Ne++]=Ke,_e[Ne++]=Ft,Ft=e;var r=Ge;e=Ke;var l=32-Ie(r)-1;r&=~(1<<l),n+=1;var o=32-Ie(t)+l;if(30<o){var i=l-l%5;o=(r&(1<<i)-1).toString(32),r>>=i,l-=i,Ge=1<<32-Ie(t)+l|n<<l|r,Ke=o+e}else Ge=1<<o|n<<l|r,Ke=e}function ki(e){e.return!==null&&(Pt(e,1),wa(e,1,0))}function Si(e){for(;e===nl;)nl=Jt[--qt],Jt[qt]=null,rl=Jt[--qt],Jt[qt]=null;for(;e===Ft;)Ft=_e[--Ne],_e[Ne]=null,Ke=_e[--Ne],_e[Ne]=null,Ge=_e[--Ne],_e[Ne]=null}var Se=null,ke=null,H=!1,Oe=null;function ka(e,t){var n=ze(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Lu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Se=e,ke=mt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Se=e,ke=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Ft!==null?{id:Ge,overflow:Ke}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ze(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Se=e,ke=null,!0):!1;default:return!1}}function Oo(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Io(e){if(H){var t=ke;if(t){var n=t;if(!Lu(e,t)){if(Oo(e))throw Error(g(418));t=mt(n.nextSibling);var r=Se;t&&Lu(e,t)?ka(r,n):(e.flags=e.flags&-4097|2,H=!1,Se=e)}}else{if(Oo(e))throw Error(g(418));e.flags=e.flags&-4097|2,H=!1,Se=e}}}function Ru(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Se=e}function Nr(e){if(e!==Se)return!1;if(!H)return Ru(e),H=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Lo(e.type,e.memoizedProps)),t&&(t=ke)){if(Oo(e))throw Sa(),Error(g(418));for(;t;)ka(e,t),t=mt(t.nextSibling)}if(Ru(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(g(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){ke=mt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}ke=null}}else ke=Se?mt(e.stateNode.nextSibling):null;return!0}function Sa(){for(var e=ke;e;)e=mt(e.nextSibling)}function dn(){ke=Se=null,H=!1}function xi(e){Oe===null?Oe=[e]:Oe.push(e)}var Nd=be.ReactCurrentBatchConfig;function Nn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(g(309));var r=n.stateNode}if(!r)throw Error(g(147,e));var l=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(i){var u=l.refs;i===null?delete u[o]:u[o]=i},t._stringRef=o,t)}if(typeof e!="string")throw Error(g(284));if(!n._owner)throw Error(g(290,e))}return e}function zr(e,t){throw e=Object.prototype.toString.call(t),Error(g(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Mu(e){var t=e._init;return t(e._payload)}function xa(e){function t(f,a){if(e){var d=f.deletions;d===null?(f.deletions=[a],f.flags|=16):d.push(a)}}function n(f,a){if(!e)return null;for(;a!==null;)t(f,a),a=a.sibling;return null}function r(f,a){for(f=new Map;a!==null;)a.key!==null?f.set(a.key,a):f.set(a.index,a),a=a.sibling;return f}function l(f,a){return f=yt(f,a),f.index=0,f.sibling=null,f}function o(f,a,d){return f.index=d,e?(d=f.alternate,d!==null?(d=d.index,d<a?(f.flags|=2,a):d):(f.flags|=2,a)):(f.flags|=1048576,a)}function i(f){return e&&f.alternate===null&&(f.flags|=2),f}function u(f,a,d,v){return a===null||a.tag!==6?(a=ro(d,f.mode,v),a.return=f,a):(a=l(a,d),a.return=f,a)}function s(f,a,d,v){var S=d.type;return S===Qt?h(f,a,d.props.children,v,d.key):a!==null&&(a.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===ot&&Mu(S)===a.type)?(v=l(a,d.props),v.ref=Nn(f,a,d),v.return=f,v):(v=Br(d.type,d.key,d.props,null,f.mode,v),v.ref=Nn(f,a,d),v.return=f,v)}function c(f,a,d,v){return a===null||a.tag!==4||a.stateNode.containerInfo!==d.containerInfo||a.stateNode.implementation!==d.implementation?(a=lo(d,f.mode,v),a.return=f,a):(a=l(a,d.children||[]),a.return=f,a)}function h(f,a,d,v,S){return a===null||a.tag!==7?(a=Ot(d,f.mode,v,S),a.return=f,a):(a=l(a,d),a.return=f,a)}function m(f,a,d){if(typeof a=="string"&&a!==""||typeof a=="number")return a=ro(""+a,f.mode,d),a.return=f,a;if(typeof a=="object"&&a!==null){switch(a.$$typeof){case vr:return d=Br(a.type,a.key,a.props,null,f.mode,d),d.ref=Nn(f,null,a),d.return=f,d;case Ht:return a=lo(a,f.mode,d),a.return=f,a;case ot:var v=a._init;return m(f,v(a._payload),d)}if(Mn(a)||Sn(a))return a=Ot(a,f.mode,d,null),a.return=f,a;zr(f,a)}return null}function p(f,a,d,v){var S=a!==null?a.key:null;if(typeof d=="string"&&d!==""||typeof d=="number")return S!==null?null:u(f,a,""+d,v);if(typeof d=="object"&&d!==null){switch(d.$$typeof){case vr:return d.key===S?s(f,a,d,v):null;case Ht:return d.key===S?c(f,a,d,v):null;case ot:return S=d._init,p(f,a,S(d._payload),v)}if(Mn(d)||Sn(d))return S!==null?null:h(f,a,d,v,null);zr(f,d)}return null}function y(f,a,d,v,S){if(typeof v=="string"&&v!==""||typeof v=="number")return f=f.get(d)||null,u(a,f,""+v,S);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case vr:return f=f.get(v.key===null?d:v.key)||null,s(a,f,v,S);case Ht:return f=f.get(v.key===null?d:v.key)||null,c(a,f,v,S);case ot:var E=v._init;return y(f,a,d,E(v._payload),S)}if(Mn(v)||Sn(v))return f=f.get(d)||null,h(a,f,v,S,null);zr(a,v)}return null}function k(f,a,d,v){for(var S=null,E=null,_=a,N=a=0,$=null;_!==null&&N<d.length;N++){_.index>N?($=_,_=null):$=_.sibling;var R=p(f,_,d[N],v);if(R===null){_===null&&(_=$);break}e&&_&&R.alternate===null&&t(f,_),a=o(R,a,N),E===null?S=R:E.sibling=R,E=R,_=$}if(N===d.length)return n(f,_),H&&Pt(f,N),S;if(_===null){for(;N<d.length;N++)_=m(f,d[N],v),_!==null&&(a=o(_,a,N),E===null?S=_:E.sibling=_,E=_);return H&&Pt(f,N),S}for(_=r(f,_);N<d.length;N++)$=y(_,f,N,d[N],v),$!==null&&(e&&$.alternate!==null&&_.delete($.key===null?N:$.key),a=o($,a,N),E===null?S=$:E.sibling=$,E=$);return e&&_.forEach(function(ae){return t(f,ae)}),H&&Pt(f,N),S}function w(f,a,d,v){var S=Sn(d);if(typeof S!="function")throw Error(g(150));if(d=S.call(d),d==null)throw Error(g(151));for(var E=S=null,_=a,N=a=0,$=null,R=d.next();_!==null&&!R.done;N++,R=d.next()){_.index>N?($=_,_=null):$=_.sibling;var ae=p(f,_,R.value,v);if(ae===null){_===null&&(_=$);break}e&&_&&ae.alternate===null&&t(f,_),a=o(ae,a,N),E===null?S=ae:E.sibling=ae,E=ae,_=$}if(R.done)return n(f,_),H&&Pt(f,N),S;if(_===null){for(;!R.done;N++,R=d.next())R=m(f,R.value,v),R!==null&&(a=o(R,a,N),E===null?S=R:E.sibling=R,E=R);return H&&Pt(f,N),S}for(_=r(f,_);!R.done;N++,R=d.next())R=y(_,f,N,R.value,v),R!==null&&(e&&R.alternate!==null&&_.delete(R.key===null?N:R.key),a=o(R,a,N),E===null?S=R:E.sibling=R,E=R);return e&&_.forEach(function(F){return t(f,F)}),H&&Pt(f,N),S}function z(f,a,d,v){if(typeof d=="object"&&d!==null&&d.type===Qt&&d.key===null&&(d=d.props.children),typeof d=="object"&&d!==null){switch(d.$$typeof){case vr:e:{for(var S=d.key,E=a;E!==null;){if(E.key===S){if(S=d.type,S===Qt){if(E.tag===7){n(f,E.sibling),a=l(E,d.props.children),a.return=f,f=a;break e}}else if(E.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===ot&&Mu(S)===E.type){n(f,E.sibling),a=l(E,d.props),a.ref=Nn(f,E,d),a.return=f,f=a;break e}n(f,E);break}else t(f,E);E=E.sibling}d.type===Qt?(a=Ot(d.props.children,f.mode,v,d.key),a.return=f,f=a):(v=Br(d.type,d.key,d.props,null,f.mode,v),v.ref=Nn(f,a,d),v.return=f,f=v)}return i(f);case Ht:e:{for(E=d.key;a!==null;){if(a.key===E)if(a.tag===4&&a.stateNode.containerInfo===d.containerInfo&&a.stateNode.implementation===d.implementation){n(f,a.sibling),a=l(a,d.children||[]),a.return=f,f=a;break e}else{n(f,a);break}else t(f,a);a=a.sibling}a=lo(d,f.mode,v),a.return=f,f=a}return i(f);case ot:return E=d._init,z(f,a,E(d._payload),v)}if(Mn(d))return k(f,a,d,v);if(Sn(d))return w(f,a,d,v);zr(f,d)}return typeof d=="string"&&d!==""||typeof d=="number"?(d=""+d,a!==null&&a.tag===6?(n(f,a.sibling),a=l(a,d),a.return=f,f=a):(n(f,a),a=ro(d,f.mode,v),a.return=f,f=a),i(f)):n(f,a)}return z}var pn=xa(!0),Ca=xa(!1),ll=xt(null),ol=null,bt=null,Ci=null;function Ei(){Ci=bt=ol=null}function _i(e){var t=ll.current;B(ll),e._currentValue=t}function Fo(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function un(e,t){ol=e,Ci=bt=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(he=!0),e.firstContext=null)}function Te(e){var t=e._currentValue;if(Ci!==e)if(e={context:e,memoizedValue:t,next:null},bt===null){if(ol===null)throw Error(g(308));bt=e,ol.dependencies={lanes:0,firstContext:e}}else bt=bt.next=e;return t}var Rt=null;function Ni(e){Rt===null?Rt=[e]:Rt.push(e)}function Ea(e,t,n,r){var l=t.interleaved;return l===null?(n.next=n,Ni(t)):(n.next=l.next,l.next=n),t.interleaved=n,Je(e,r)}function Je(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var it=!1;function zi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function _a(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ye(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function ht(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,O&2){var l=r.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),r.pending=t,Je(e,n)}return l=r.interleaved,l===null?(t.next=t,Ni(r)):(t.next=l.next,l.next=t),r.interleaved=t,Je(e,n)}function Dr(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,di(e,n)}}function ju(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var l=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?l=o=i:o=o.next=i,n=n.next}while(n!==null);o===null?l=o=t:o=o.next=t}else l=o=t;n={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function il(e,t,n,r){var l=e.updateQueue;it=!1;var o=l.firstBaseUpdate,i=l.lastBaseUpdate,u=l.shared.pending;if(u!==null){l.shared.pending=null;var s=u,c=s.next;s.next=null,i===null?o=c:i.next=c,i=s;var h=e.alternate;h!==null&&(h=h.updateQueue,u=h.lastBaseUpdate,u!==i&&(u===null?h.firstBaseUpdate=c:u.next=c,h.lastBaseUpdate=s))}if(o!==null){var m=l.baseState;i=0,h=c=s=null,u=o;do{var p=u.lane,y=u.eventTime;if((r&p)===p){h!==null&&(h=h.next={eventTime:y,lane:0,tag:u.tag,payload:u.payload,callback:u.callback,next:null});e:{var k=e,w=u;switch(p=t,y=n,w.tag){case 1:if(k=w.payload,typeof k=="function"){m=k.call(y,m,p);break e}m=k;break e;case 3:k.flags=k.flags&-65537|128;case 0:if(k=w.payload,p=typeof k=="function"?k.call(y,m,p):k,p==null)break e;m=K({},m,p);break e;case 2:it=!0}}u.callback!==null&&u.lane!==0&&(e.flags|=64,p=l.effects,p===null?l.effects=[u]:p.push(u))}else y={eventTime:y,lane:p,tag:u.tag,payload:u.payload,callback:u.callback,next:null},h===null?(c=h=y,s=m):h=h.next=y,i|=p;if(u=u.next,u===null){if(u=l.shared.pending,u===null)break;p=u,u=p.next,p.next=null,l.lastBaseUpdate=p,l.shared.pending=null}}while(!0);if(h===null&&(s=m),l.baseState=s,l.firstBaseUpdate=c,l.lastBaseUpdate=h,t=l.shared.interleaved,t!==null){l=t;do i|=l.lane,l=l.next;while(l!==t)}else o===null&&(l.shared.lanes=0);At|=i,e.lanes=i,e.memoizedState=m}}function Ou(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],l=r.callback;if(l!==null){if(r.callback=null,r=n,typeof l!="function")throw Error(g(191,l));l.call(r)}}}var fr={},We=xt(fr),tr=xt(fr),nr=xt(fr);function Mt(e){if(e===fr)throw Error(g(174));return e}function Pi(e,t){switch(U(nr,t),U(tr,e),U(We,fr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:vo(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=vo(t,e)}B(We),U(We,t)}function mn(){B(We),B(tr),B(nr)}function Na(e){Mt(nr.current);var t=Mt(We.current),n=vo(t,e.type);t!==n&&(U(tr,e),U(We,n))}function Ti(e){tr.current===e&&(B(We),B(tr))}var Q=xt(0);function ul(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Jl=[];function Li(){for(var e=0;e<Jl.length;e++)Jl[e]._workInProgressVersionPrimary=null;Jl.length=0}var Ar=be.ReactCurrentDispatcher,ql=be.ReactCurrentBatchConfig,Dt=0,G=null,J=null,ee=null,sl=!1,$n=!1,rr=0,zd=0;function oe(){throw Error(g(321))}function Ri(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!De(e[n],t[n]))return!1;return!0}function Mi(e,t,n,r,l,o){if(Dt=o,G=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ar.current=e===null||e.memoizedState===null?Rd:Md,e=n(r,l),$n){o=0;do{if($n=!1,rr=0,25<=o)throw Error(g(301));o+=1,ee=J=null,t.updateQueue=null,Ar.current=jd,e=n(r,l)}while($n)}if(Ar.current=al,t=J!==null&&J.next!==null,Dt=0,ee=J=G=null,sl=!1,t)throw Error(g(300));return e}function ji(){var e=rr!==0;return rr=0,e}function Ue(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ee===null?G.memoizedState=ee=e:ee=ee.next=e,ee}function Le(){if(J===null){var e=G.alternate;e=e!==null?e.memoizedState:null}else e=J.next;var t=ee===null?G.memoizedState:ee.next;if(t!==null)ee=t,J=e;else{if(e===null)throw Error(g(310));J=e,e={memoizedState:J.memoizedState,baseState:J.baseState,baseQueue:J.baseQueue,queue:J.queue,next:null},ee===null?G.memoizedState=ee=e:ee=ee.next=e}return ee}function lr(e,t){return typeof t=="function"?t(e):t}function bl(e){var t=Le(),n=t.queue;if(n===null)throw Error(g(311));n.lastRenderedReducer=e;var r=J,l=r.baseQueue,o=n.pending;if(o!==null){if(l!==null){var i=l.next;l.next=o.next,o.next=i}r.baseQueue=l=o,n.pending=null}if(l!==null){o=l.next,r=r.baseState;var u=i=null,s=null,c=o;do{var h=c.lane;if((Dt&h)===h)s!==null&&(s=s.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var m={lane:h,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};s===null?(u=s=m,i=r):s=s.next=m,G.lanes|=h,At|=h}c=c.next}while(c!==null&&c!==o);s===null?i=r:s.next=u,De(r,t.memoizedState)||(he=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=s,n.lastRenderedState=r}if(e=n.interleaved,e!==null){l=e;do o=l.lane,G.lanes|=o,At|=o,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function eo(e){var t=Le(),n=t.queue;if(n===null)throw Error(g(311));n.lastRenderedReducer=e;var r=n.dispatch,l=n.pending,o=t.memoizedState;if(l!==null){n.pending=null;var i=l=l.next;do o=e(o,i.action),i=i.next;while(i!==l);De(o,t.memoizedState)||(he=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function za(){}function Pa(e,t){var n=G,r=Le(),l=t(),o=!De(r.memoizedState,l);if(o&&(r.memoizedState=l,he=!0),r=r.queue,Oi(Ra.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||ee!==null&&ee.memoizedState.tag&1){if(n.flags|=2048,or(9,La.bind(null,n,r,l,t),void 0,null),te===null)throw Error(g(349));Dt&30||Ta(n,t,l)}return l}function Ta(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=G.updateQueue,t===null?(t={lastEffect:null,stores:null},G.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function La(e,t,n,r){t.value=n,t.getSnapshot=r,Ma(t)&&ja(e)}function Ra(e,t,n){return n(function(){Ma(t)&&ja(e)})}function Ma(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!De(e,n)}catch{return!0}}function ja(e){var t=Je(e,1);t!==null&&Fe(t,e,1,-1)}function Iu(e){var t=Ue();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:lr,lastRenderedState:e},t.queue=e,e=e.dispatch=Ld.bind(null,G,e),[t.memoizedState,e]}function or(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=G.updateQueue,t===null?(t={lastEffect:null,stores:null},G.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Oa(){return Le().memoizedState}function Ur(e,t,n,r){var l=Ue();G.flags|=e,l.memoizedState=or(1|t,n,void 0,r===void 0?null:r)}function xl(e,t,n,r){var l=Le();r=r===void 0?null:r;var o=void 0;if(J!==null){var i=J.memoizedState;if(o=i.destroy,r!==null&&Ri(r,i.deps)){l.memoizedState=or(t,n,o,r);return}}G.flags|=e,l.memoizedState=or(1|t,n,o,r)}function Fu(e,t){return Ur(8390656,8,e,t)}function Oi(e,t){return xl(2048,8,e,t)}function Ia(e,t){return xl(4,2,e,t)}function Fa(e,t){return xl(4,4,e,t)}function Da(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Aa(e,t,n){return n=n!=null?n.concat([e]):null,xl(4,4,Da.bind(null,t,e),n)}function Ii(){}function Ua(e,t){var n=Le();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ri(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function $a(e,t){var n=Le();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ri(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Va(e,t,n){return Dt&21?(De(n,t)||(n=Gs(),G.lanes|=n,At|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,he=!0),e.memoizedState=n)}function Pd(e,t){var n=A;A=n!==0&&4>n?n:4,e(!0);var r=ql.transition;ql.transition={};try{e(!1),t()}finally{A=n,ql.transition=r}}function Wa(){return Le().memoizedState}function Td(e,t,n){var r=gt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Ba(e))Ha(t,n);else if(n=Ea(e,t,n,r),n!==null){var l=fe();Fe(n,e,r,l),Qa(n,t,r)}}function Ld(e,t,n){var r=gt(e),l={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ba(e))Ha(t,l);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var i=t.lastRenderedState,u=o(i,n);if(l.hasEagerState=!0,l.eagerState=u,De(u,i)){var s=t.interleaved;s===null?(l.next=l,Ni(t)):(l.next=s.next,s.next=l),t.interleaved=l;return}}catch{}finally{}n=Ea(e,t,l,r),n!==null&&(l=fe(),Fe(n,e,r,l),Qa(n,t,r))}}function Ba(e){var t=e.alternate;return e===G||t!==null&&t===G}function Ha(e,t){$n=sl=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Qa(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,di(e,n)}}var al={readContext:Te,useCallback:oe,useContext:oe,useEffect:oe,useImperativeHandle:oe,useInsertionEffect:oe,useLayoutEffect:oe,useMemo:oe,useReducer:oe,useRef:oe,useState:oe,useDebugValue:oe,useDeferredValue:oe,useTransition:oe,useMutableSource:oe,useSyncExternalStore:oe,useId:oe,unstable_isNewReconciler:!1},Rd={readContext:Te,useCallback:function(e,t){return Ue().memoizedState=[e,t===void 0?null:t],e},useContext:Te,useEffect:Fu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Ur(4194308,4,Da.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ur(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ur(4,2,e,t)},useMemo:function(e,t){var n=Ue();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ue();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Td.bind(null,G,e),[r.memoizedState,e]},useRef:function(e){var t=Ue();return e={current:e},t.memoizedState=e},useState:Iu,useDebugValue:Ii,useDeferredValue:function(e){return Ue().memoizedState=e},useTransition:function(){var e=Iu(!1),t=e[0];return e=Pd.bind(null,e[1]),Ue().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=G,l=Ue();if(H){if(n===void 0)throw Error(g(407));n=n()}else{if(n=t(),te===null)throw Error(g(349));Dt&30||Ta(r,t,n)}l.memoizedState=n;var o={value:n,getSnapshot:t};return l.queue=o,Fu(Ra.bind(null,r,o,e),[e]),r.flags|=2048,or(9,La.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=Ue(),t=te.identifierPrefix;if(H){var n=Ke,r=Ge;n=(r&~(1<<32-Ie(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=rr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=zd++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Md={readContext:Te,useCallback:Ua,useContext:Te,useEffect:Oi,useImperativeHandle:Aa,useInsertionEffect:Ia,useLayoutEffect:Fa,useMemo:$a,useReducer:bl,useRef:Oa,useState:function(){return bl(lr)},useDebugValue:Ii,useDeferredValue:function(e){var t=Le();return Va(t,J.memoizedState,e)},useTransition:function(){var e=bl(lr)[0],t=Le().memoizedState;return[e,t]},useMutableSource:za,useSyncExternalStore:Pa,useId:Wa,unstable_isNewReconciler:!1},jd={readContext:Te,useCallback:Ua,useContext:Te,useEffect:Oi,useImperativeHandle:Aa,useInsertionEffect:Ia,useLayoutEffect:Fa,useMemo:$a,useReducer:eo,useRef:Oa,useState:function(){return eo(lr)},useDebugValue:Ii,useDeferredValue:function(e){var t=Le();return J===null?t.memoizedState=e:Va(t,J.memoizedState,e)},useTransition:function(){var e=eo(lr)[0],t=Le().memoizedState;return[e,t]},useMutableSource:za,useSyncExternalStore:Pa,useId:Wa,unstable_isNewReconciler:!1};function Me(e,t){if(e&&e.defaultProps){t=K({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Do(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:K({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Cl={isMounted:function(e){return(e=e._reactInternals)?Vt(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=fe(),l=gt(e),o=Ye(r,l);o.payload=t,n!=null&&(o.callback=n),t=ht(e,o,l),t!==null&&(Fe(t,e,l,r),Dr(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=fe(),l=gt(e),o=Ye(r,l);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=ht(e,o,l),t!==null&&(Fe(t,e,l,r),Dr(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=fe(),r=gt(e),l=Ye(n,r);l.tag=2,t!=null&&(l.callback=t),t=ht(e,l,r),t!==null&&(Fe(t,e,r,n),Dr(t,e,r))}};function Du(e,t,n,r,l,o,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,i):t.prototype&&t.prototype.isPureReactComponent?!Jn(n,r)||!Jn(l,o):!0}function Ga(e,t,n){var r=!1,l=kt,o=t.contextType;return typeof o=="object"&&o!==null?o=Te(o):(l=ge(t)?It:se.current,r=t.contextTypes,o=(r=r!=null)?fn(e,l):kt),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Cl,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=o),t}function Au(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Cl.enqueueReplaceState(t,t.state,null)}function Ao(e,t,n,r){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},zi(e);var o=t.contextType;typeof o=="object"&&o!==null?l.context=Te(o):(o=ge(t)?It:se.current,l.context=fn(e,o)),l.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Do(e,t,o,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&Cl.enqueueReplaceState(l,l.state,null),il(e,n,l,r),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function hn(e,t){try{var n="",r=t;do n+=uf(r),r=r.return;while(r);var l=n}catch(o){l=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:l,digest:null}}function to(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Uo(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Od=typeof WeakMap=="function"?WeakMap:Map;function Ka(e,t,n){n=Ye(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){fl||(fl=!0,Xo=r),Uo(e,t)},n}function Ya(e,t,n){n=Ye(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var l=t.value;n.payload=function(){return r(l)},n.callback=function(){Uo(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){Uo(e,t),typeof r!="function"&&(vt===null?vt=new Set([this]):vt.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function Uu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Od;var l=new Set;r.set(t,l)}else l=r.get(t),l===void 0&&(l=new Set,r.set(t,l));l.has(n)||(l.add(n),e=Yd.bind(null,e,t,n),t.then(e,e))}function $u(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Vu(e,t,n,r,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Ye(-1,1),t.tag=2,ht(n,t,1))),n.lanes|=1),e)}var Id=be.ReactCurrentOwner,he=!1;function ce(e,t,n,r){t.child=e===null?Ca(t,null,n,r):pn(t,e.child,n,r)}function Wu(e,t,n,r,l){n=n.render;var o=t.ref;return un(t,l),r=Mi(e,t,n,r,o,l),n=ji(),e!==null&&!he?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,qe(e,t,l)):(H&&n&&ki(t),t.flags|=1,ce(e,t,r,l),t.child)}function Bu(e,t,n,r,l){if(e===null){var o=n.type;return typeof o=="function"&&!Bi(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,Xa(e,t,o,r,l)):(e=Br(n.type,null,r,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&l)){var i=o.memoizedProps;if(n=n.compare,n=n!==null?n:Jn,n(i,r)&&e.ref===t.ref)return qe(e,t,l)}return t.flags|=1,e=yt(o,r),e.ref=t.ref,e.return=t,t.child=e}function Xa(e,t,n,r,l){if(e!==null){var o=e.memoizedProps;if(Jn(o,r)&&e.ref===t.ref)if(he=!1,t.pendingProps=r=o,(e.lanes&l)!==0)e.flags&131072&&(he=!0);else return t.lanes=e.lanes,qe(e,t,l)}return $o(e,t,n,r,l)}function Za(e,t,n){var r=t.pendingProps,l=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},U(tn,we),we|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,U(tn,we),we|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,U(tn,we),we|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,U(tn,we),we|=r;return ce(e,t,l,n),t.child}function Ja(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function $o(e,t,n,r,l){var o=ge(n)?It:se.current;return o=fn(t,o),un(t,l),n=Mi(e,t,n,r,o,l),r=ji(),e!==null&&!he?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,qe(e,t,l)):(H&&r&&ki(t),t.flags|=1,ce(e,t,n,l),t.child)}function Hu(e,t,n,r,l){if(ge(n)){var o=!0;tl(t)}else o=!1;if(un(t,l),t.stateNode===null)$r(e,t),Ga(t,n,r),Ao(t,n,r,l),r=!0;else if(e===null){var i=t.stateNode,u=t.memoizedProps;i.props=u;var s=i.context,c=n.contextType;typeof c=="object"&&c!==null?c=Te(c):(c=ge(n)?It:se.current,c=fn(t,c));var h=n.getDerivedStateFromProps,m=typeof h=="function"||typeof i.getSnapshotBeforeUpdate=="function";m||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(u!==r||s!==c)&&Au(t,i,r,c),it=!1;var p=t.memoizedState;i.state=p,il(t,r,i,l),s=t.memoizedState,u!==r||p!==s||ve.current||it?(typeof h=="function"&&(Do(t,n,h,r),s=t.memoizedState),(u=it||Du(t,n,u,r,p,s,c))?(m||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),i.props=r,i.state=s,i.context=c,r=u):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,_a(e,t),u=t.memoizedProps,c=t.type===t.elementType?u:Me(t.type,u),i.props=c,m=t.pendingProps,p=i.context,s=n.contextType,typeof s=="object"&&s!==null?s=Te(s):(s=ge(n)?It:se.current,s=fn(t,s));var y=n.getDerivedStateFromProps;(h=typeof y=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(u!==m||p!==s)&&Au(t,i,r,s),it=!1,p=t.memoizedState,i.state=p,il(t,r,i,l);var k=t.memoizedState;u!==m||p!==k||ve.current||it?(typeof y=="function"&&(Do(t,n,y,r),k=t.memoizedState),(c=it||Du(t,n,c,r,p,k,s)||!1)?(h||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(r,k,s),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(r,k,s)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||u===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=k),i.props=r,i.state=k,i.context=s,r=c):(typeof i.componentDidUpdate!="function"||u===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return Vo(e,t,n,r,o,l)}function Vo(e,t,n,r,l,o){Ja(e,t);var i=(t.flags&128)!==0;if(!r&&!i)return l&&Tu(t,n,!1),qe(e,t,o);r=t.stateNode,Id.current=t;var u=i&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&i?(t.child=pn(t,e.child,null,o),t.child=pn(t,null,u,o)):ce(e,t,u,o),t.memoizedState=r.state,l&&Tu(t,n,!0),t.child}function qa(e){var t=e.stateNode;t.pendingContext?Pu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Pu(e,t.context,!1),Pi(e,t.containerInfo)}function Qu(e,t,n,r,l){return dn(),xi(l),t.flags|=256,ce(e,t,n,r),t.child}var Wo={dehydrated:null,treeContext:null,retryLane:0};function Bo(e){return{baseLanes:e,cachePool:null,transitions:null}}function ba(e,t,n){var r=t.pendingProps,l=Q.current,o=!1,i=(t.flags&128)!==0,u;if((u=i)||(u=e!==null&&e.memoizedState===null?!1:(l&2)!==0),u?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),U(Q,l&1),e===null)return Io(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=r.children,e=r.fallback,o?(r=t.mode,o=t.child,i={mode:"hidden",children:i},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=i):o=Nl(i,r,0,null),e=Ot(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=Bo(n),t.memoizedState=Wo,e):Fi(t,i));if(l=e.memoizedState,l!==null&&(u=l.dehydrated,u!==null))return Fd(e,t,i,r,u,l,n);if(o){o=r.fallback,i=t.mode,l=e.child,u=l.sibling;var s={mode:"hidden",children:r.children};return!(i&1)&&t.child!==l?(r=t.child,r.childLanes=0,r.pendingProps=s,t.deletions=null):(r=yt(l,s),r.subtreeFlags=l.subtreeFlags&14680064),u!==null?o=yt(u,o):(o=Ot(o,i,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,i=e.child.memoizedState,i=i===null?Bo(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},o.memoizedState=i,o.childLanes=e.childLanes&~n,t.memoizedState=Wo,r}return o=e.child,e=o.sibling,r=yt(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Fi(e,t){return t=Nl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Pr(e,t,n,r){return r!==null&&xi(r),pn(t,e.child,null,n),e=Fi(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Fd(e,t,n,r,l,o,i){if(n)return t.flags&256?(t.flags&=-257,r=to(Error(g(422))),Pr(e,t,i,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,l=t.mode,r=Nl({mode:"visible",children:r.children},l,0,null),o=Ot(o,l,i,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&pn(t,e.child,null,i),t.child.memoizedState=Bo(i),t.memoizedState=Wo,o);if(!(t.mode&1))return Pr(e,t,i,null);if(l.data==="$!"){if(r=l.nextSibling&&l.nextSibling.dataset,r)var u=r.dgst;return r=u,o=Error(g(419)),r=to(o,r,void 0),Pr(e,t,i,r)}if(u=(i&e.childLanes)!==0,he||u){if(r=te,r!==null){switch(i&-i){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(r.suspendedLanes|i)?0:l,l!==0&&l!==o.retryLane&&(o.retryLane=l,Je(e,l),Fe(r,e,l,-1))}return Wi(),r=to(Error(g(421))),Pr(e,t,i,r)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=Xd.bind(null,e),l._reactRetry=t,null):(e=o.treeContext,ke=mt(l.nextSibling),Se=t,H=!0,Oe=null,e!==null&&(_e[Ne++]=Ge,_e[Ne++]=Ke,_e[Ne++]=Ft,Ge=e.id,Ke=e.overflow,Ft=t),t=Fi(t,r.children),t.flags|=4096,t)}function Gu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Fo(e.return,t,n)}function no(e,t,n,r,l){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:l}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=l)}function ec(e,t,n){var r=t.pendingProps,l=r.revealOrder,o=r.tail;if(ce(e,t,r.children,n),r=Q.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Gu(e,n,t);else if(e.tag===19)Gu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(U(Q,r),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&ul(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),no(t,!1,l,n,o);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&ul(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}no(t,!0,n,null,o);break;case"together":no(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function $r(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function qe(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),At|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(g(153));if(t.child!==null){for(e=t.child,n=yt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=yt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Dd(e,t,n){switch(t.tag){case 3:qa(t),dn();break;case 5:Na(t);break;case 1:ge(t.type)&&tl(t);break;case 4:Pi(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,l=t.memoizedProps.value;U(ll,r._currentValue),r._currentValue=l;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(U(Q,Q.current&1),t.flags|=128,null):n&t.child.childLanes?ba(e,t,n):(U(Q,Q.current&1),e=qe(e,t,n),e!==null?e.sibling:null);U(Q,Q.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return ec(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),U(Q,Q.current),r)break;return null;case 22:case 23:return t.lanes=0,Za(e,t,n)}return qe(e,t,n)}var tc,Ho,nc,rc;tc=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Ho=function(){};nc=function(e,t,n,r){var l=e.memoizedProps;if(l!==r){e=t.stateNode,Mt(We.current);var o=null;switch(n){case"input":l=fo(e,l),r=fo(e,r),o=[];break;case"select":l=K({},l,{value:void 0}),r=K({},r,{value:void 0}),o=[];break;case"textarea":l=ho(e,l),r=ho(e,r),o=[];break;default:typeof l.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=br)}go(n,r);var i;n=null;for(c in l)if(!r.hasOwnProperty(c)&&l.hasOwnProperty(c)&&l[c]!=null)if(c==="style"){var u=l[c];for(i in u)u.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(Hn.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in r){var s=r[c];if(u=l!=null?l[c]:void 0,r.hasOwnProperty(c)&&s!==u&&(s!=null||u!=null))if(c==="style")if(u){for(i in u)!u.hasOwnProperty(i)||s&&s.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in s)s.hasOwnProperty(i)&&u[i]!==s[i]&&(n||(n={}),n[i]=s[i])}else n||(o||(o=[]),o.push(c,n)),n=s;else c==="dangerouslySetInnerHTML"?(s=s?s.__html:void 0,u=u?u.__html:void 0,s!=null&&u!==s&&(o=o||[]).push(c,s)):c==="children"?typeof s!="string"&&typeof s!="number"||(o=o||[]).push(c,""+s):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(Hn.hasOwnProperty(c)?(s!=null&&c==="onScroll"&&W("scroll",e),o||u===s||(o=[])):(o=o||[]).push(c,s))}n&&(o=o||[]).push("style",n);var c=o;(t.updateQueue=c)&&(t.flags|=4)}};rc=function(e,t,n,r){n!==r&&(t.flags|=4)};function zn(e,t){if(!H)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ie(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags&14680064,r|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Ad(e,t,n){var r=t.pendingProps;switch(Si(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ie(t),null;case 1:return ge(t.type)&&el(),ie(t),null;case 3:return r=t.stateNode,mn(),B(ve),B(se),Li(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Nr(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Oe!==null&&(qo(Oe),Oe=null))),Ho(e,t),ie(t),null;case 5:Ti(t);var l=Mt(nr.current);if(n=t.type,e!==null&&t.stateNode!=null)nc(e,t,n,r,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(g(166));return ie(t),null}if(e=Mt(We.current),Nr(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[$e]=t,r[er]=o,e=(t.mode&1)!==0,n){case"dialog":W("cancel",r),W("close",r);break;case"iframe":case"object":case"embed":W("load",r);break;case"video":case"audio":for(l=0;l<On.length;l++)W(On[l],r);break;case"source":W("error",r);break;case"img":case"image":case"link":W("error",r),W("load",r);break;case"details":W("toggle",r);break;case"input":tu(r,o),W("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},W("invalid",r);break;case"textarea":ru(r,o),W("invalid",r)}go(n,o),l=null;for(var i in o)if(o.hasOwnProperty(i)){var u=o[i];i==="children"?typeof u=="string"?r.textContent!==u&&(o.suppressHydrationWarning!==!0&&_r(r.textContent,u,e),l=["children",u]):typeof u=="number"&&r.textContent!==""+u&&(o.suppressHydrationWarning!==!0&&_r(r.textContent,u,e),l=["children",""+u]):Hn.hasOwnProperty(i)&&u!=null&&i==="onScroll"&&W("scroll",r)}switch(n){case"input":gr(r),nu(r,o,!0);break;case"textarea":gr(r),lu(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=br)}r=l,t.updateQueue=r,r!==null&&(t.flags|=4)}else{i=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Rs(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),n==="select"&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[$e]=t,e[er]=r,tc(e,t,!1,!1),t.stateNode=e;e:{switch(i=yo(n,r),n){case"dialog":W("cancel",e),W("close",e),l=r;break;case"iframe":case"object":case"embed":W("load",e),l=r;break;case"video":case"audio":for(l=0;l<On.length;l++)W(On[l],e);l=r;break;case"source":W("error",e),l=r;break;case"img":case"image":case"link":W("error",e),W("load",e),l=r;break;case"details":W("toggle",e),l=r;break;case"input":tu(e,r),l=fo(e,r),W("invalid",e);break;case"option":l=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},l=K({},r,{value:void 0}),W("invalid",e);break;case"textarea":ru(e,r),l=ho(e,r),W("invalid",e);break;default:l=r}go(n,l),u=l;for(o in u)if(u.hasOwnProperty(o)){var s=u[o];o==="style"?Os(e,s):o==="dangerouslySetInnerHTML"?(s=s?s.__html:void 0,s!=null&&Ms(e,s)):o==="children"?typeof s=="string"?(n!=="textarea"||s!=="")&&Qn(e,s):typeof s=="number"&&Qn(e,""+s):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Hn.hasOwnProperty(o)?s!=null&&o==="onScroll"&&W("scroll",e):s!=null&&ii(e,o,s,i))}switch(n){case"input":gr(e),nu(e,r,!1);break;case"textarea":gr(e),lu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+wt(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?nn(e,!!r.multiple,o,!1):r.defaultValue!=null&&nn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=br)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ie(t),null;case 6:if(e&&t.stateNode!=null)rc(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(g(166));if(n=Mt(nr.current),Mt(We.current),Nr(t)){if(r=t.stateNode,n=t.memoizedProps,r[$e]=t,(o=r.nodeValue!==n)&&(e=Se,e!==null))switch(e.tag){case 3:_r(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&_r(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[$e]=t,t.stateNode=r}return ie(t),null;case 13:if(B(Q),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(H&&ke!==null&&t.mode&1&&!(t.flags&128))Sa(),dn(),t.flags|=98560,o=!1;else if(o=Nr(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(g(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(g(317));o[$e]=t}else dn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ie(t),o=!1}else Oe!==null&&(qo(Oe),Oe=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||Q.current&1?q===0&&(q=3):Wi())),t.updateQueue!==null&&(t.flags|=4),ie(t),null);case 4:return mn(),Ho(e,t),e===null&&qn(t.stateNode.containerInfo),ie(t),null;case 10:return _i(t.type._context),ie(t),null;case 17:return ge(t.type)&&el(),ie(t),null;case 19:if(B(Q),o=t.memoizedState,o===null)return ie(t),null;if(r=(t.flags&128)!==0,i=o.rendering,i===null)if(r)zn(o,!1);else{if(q!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=ul(e),i!==null){for(t.flags|=128,zn(o,!1),r=i.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,i=o.alternate,i===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=i.childLanes,o.lanes=i.lanes,o.child=i.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=i.memoizedProps,o.memoizedState=i.memoizedState,o.updateQueue=i.updateQueue,o.type=i.type,e=i.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return U(Q,Q.current&1|2),t.child}e=e.sibling}o.tail!==null&&X()>vn&&(t.flags|=128,r=!0,zn(o,!1),t.lanes=4194304)}else{if(!r)if(e=ul(i),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),zn(o,!0),o.tail===null&&o.tailMode==="hidden"&&!i.alternate&&!H)return ie(t),null}else 2*X()-o.renderingStartTime>vn&&n!==1073741824&&(t.flags|=128,r=!0,zn(o,!1),t.lanes=4194304);o.isBackwards?(i.sibling=t.child,t.child=i):(n=o.last,n!==null?n.sibling=i:t.child=i,o.last=i)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=X(),t.sibling=null,n=Q.current,U(Q,r?n&1|2:n&1),t):(ie(t),null);case 22:case 23:return Vi(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?we&1073741824&&(ie(t),t.subtreeFlags&6&&(t.flags|=8192)):ie(t),null;case 24:return null;case 25:return null}throw Error(g(156,t.tag))}function Ud(e,t){switch(Si(t),t.tag){case 1:return ge(t.type)&&el(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return mn(),B(ve),B(se),Li(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ti(t),null;case 13:if(B(Q),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(g(340));dn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return B(Q),null;case 4:return mn(),null;case 10:return _i(t.type._context),null;case 22:case 23:return Vi(),null;case 24:return null;default:return null}}var Tr=!1,ue=!1,$d=typeof WeakSet=="function"?WeakSet:Set,C=null;function en(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Y(e,t,r)}else n.current=null}function Qo(e,t,n){try{n()}catch(r){Y(e,t,r)}}var Ku=!1;function Vd(e,t){if(Po=Zr,e=sa(),wi(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var l=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var i=0,u=-1,s=-1,c=0,h=0,m=e,p=null;t:for(;;){for(var y;m!==n||l!==0&&m.nodeType!==3||(u=i+l),m!==o||r!==0&&m.nodeType!==3||(s=i+r),m.nodeType===3&&(i+=m.nodeValue.length),(y=m.firstChild)!==null;)p=m,m=y;for(;;){if(m===e)break t;if(p===n&&++c===l&&(u=i),p===o&&++h===r&&(s=i),(y=m.nextSibling)!==null)break;m=p,p=m.parentNode}m=y}n=u===-1||s===-1?null:{start:u,end:s}}else n=null}n=n||{start:0,end:0}}else n=null;for(To={focusedElem:e,selectionRange:n},Zr=!1,C=t;C!==null;)if(t=C,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,C=e;else for(;C!==null;){t=C;try{var k=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(k!==null){var w=k.memoizedProps,z=k.memoizedState,f=t.stateNode,a=f.getSnapshotBeforeUpdate(t.elementType===t.type?w:Me(t.type,w),z);f.__reactInternalSnapshotBeforeUpdate=a}break;case 3:var d=t.stateNode.containerInfo;d.nodeType===1?d.textContent="":d.nodeType===9&&d.documentElement&&d.removeChild(d.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(g(163))}}catch(v){Y(t,t.return,v)}if(e=t.sibling,e!==null){e.return=t.return,C=e;break}C=t.return}return k=Ku,Ku=!1,k}function Vn(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var l=r=r.next;do{if((l.tag&e)===e){var o=l.destroy;l.destroy=void 0,o!==void 0&&Qo(t,n,o)}l=l.next}while(l!==r)}}function El(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Go(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function lc(e){var t=e.alternate;t!==null&&(e.alternate=null,lc(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[$e],delete t[er],delete t[Mo],delete t[Cd],delete t[Ed])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function oc(e){return e.tag===5||e.tag===3||e.tag===4}function Yu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||oc(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ko(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=br));else if(r!==4&&(e=e.child,e!==null))for(Ko(e,t,n),e=e.sibling;e!==null;)Ko(e,t,n),e=e.sibling}function Yo(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Yo(e,t,n),e=e.sibling;e!==null;)Yo(e,t,n),e=e.sibling}var ne=null,je=!1;function nt(e,t,n){for(n=n.child;n!==null;)ic(e,t,n),n=n.sibling}function ic(e,t,n){if(Ve&&typeof Ve.onCommitFiberUnmount=="function")try{Ve.onCommitFiberUnmount(vl,n)}catch{}switch(n.tag){case 5:ue||en(n,t);case 6:var r=ne,l=je;ne=null,nt(e,t,n),ne=r,je=l,ne!==null&&(je?(e=ne,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ne.removeChild(n.stateNode));break;case 18:ne!==null&&(je?(e=ne,n=n.stateNode,e.nodeType===8?Xl(e.parentNode,n):e.nodeType===1&&Xl(e,n),Xn(e)):Xl(ne,n.stateNode));break;case 4:r=ne,l=je,ne=n.stateNode.containerInfo,je=!0,nt(e,t,n),ne=r,je=l;break;case 0:case 11:case 14:case 15:if(!ue&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){l=r=r.next;do{var o=l,i=o.destroy;o=o.tag,i!==void 0&&(o&2||o&4)&&Qo(n,t,i),l=l.next}while(l!==r)}nt(e,t,n);break;case 1:if(!ue&&(en(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(u){Y(n,t,u)}nt(e,t,n);break;case 21:nt(e,t,n);break;case 22:n.mode&1?(ue=(r=ue)||n.memoizedState!==null,nt(e,t,n),ue=r):nt(e,t,n);break;default:nt(e,t,n)}}function Xu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new $d),t.forEach(function(r){var l=Zd.bind(null,e,r);n.has(r)||(n.add(r),r.then(l,l))})}}function Re(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var l=n[r];try{var o=e,i=t,u=i;e:for(;u!==null;){switch(u.tag){case 5:ne=u.stateNode,je=!1;break e;case 3:ne=u.stateNode.containerInfo,je=!0;break e;case 4:ne=u.stateNode.containerInfo,je=!0;break e}u=u.return}if(ne===null)throw Error(g(160));ic(o,i,l),ne=null,je=!1;var s=l.alternate;s!==null&&(s.return=null),l.return=null}catch(c){Y(l,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)uc(t,e),t=t.sibling}function uc(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Re(t,e),Ae(e),r&4){try{Vn(3,e,e.return),El(3,e)}catch(w){Y(e,e.return,w)}try{Vn(5,e,e.return)}catch(w){Y(e,e.return,w)}}break;case 1:Re(t,e),Ae(e),r&512&&n!==null&&en(n,n.return);break;case 5:if(Re(t,e),Ae(e),r&512&&n!==null&&en(n,n.return),e.flags&32){var l=e.stateNode;try{Qn(l,"")}catch(w){Y(e,e.return,w)}}if(r&4&&(l=e.stateNode,l!=null)){var o=e.memoizedProps,i=n!==null?n.memoizedProps:o,u=e.type,s=e.updateQueue;if(e.updateQueue=null,s!==null)try{u==="input"&&o.type==="radio"&&o.name!=null&&Ts(l,o),yo(u,i);var c=yo(u,o);for(i=0;i<s.length;i+=2){var h=s[i],m=s[i+1];h==="style"?Os(l,m):h==="dangerouslySetInnerHTML"?Ms(l,m):h==="children"?Qn(l,m):ii(l,h,m,c)}switch(u){case"input":po(l,o);break;case"textarea":Ls(l,o);break;case"select":var p=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!o.multiple;var y=o.value;y!=null?nn(l,!!o.multiple,y,!1):p!==!!o.multiple&&(o.defaultValue!=null?nn(l,!!o.multiple,o.defaultValue,!0):nn(l,!!o.multiple,o.multiple?[]:"",!1))}l[er]=o}catch(w){Y(e,e.return,w)}}break;case 6:if(Re(t,e),Ae(e),r&4){if(e.stateNode===null)throw Error(g(162));l=e.stateNode,o=e.memoizedProps;try{l.nodeValue=o}catch(w){Y(e,e.return,w)}}break;case 3:if(Re(t,e),Ae(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Xn(t.containerInfo)}catch(w){Y(e,e.return,w)}break;case 4:Re(t,e),Ae(e);break;case 13:Re(t,e),Ae(e),l=e.child,l.flags&8192&&(o=l.memoizedState!==null,l.stateNode.isHidden=o,!o||l.alternate!==null&&l.alternate.memoizedState!==null||(Ui=X())),r&4&&Xu(e);break;case 22:if(h=n!==null&&n.memoizedState!==null,e.mode&1?(ue=(c=ue)||h,Re(t,e),ue=c):Re(t,e),Ae(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!h&&e.mode&1)for(C=e,h=e.child;h!==null;){for(m=C=h;C!==null;){switch(p=C,y=p.child,p.tag){case 0:case 11:case 14:case 15:Vn(4,p,p.return);break;case 1:en(p,p.return);var k=p.stateNode;if(typeof k.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,k.props=t.memoizedProps,k.state=t.memoizedState,k.componentWillUnmount()}catch(w){Y(r,n,w)}}break;case 5:en(p,p.return);break;case 22:if(p.memoizedState!==null){Ju(m);continue}}y!==null?(y.return=p,C=y):Ju(m)}h=h.sibling}e:for(h=null,m=e;;){if(m.tag===5){if(h===null){h=m;try{l=m.stateNode,c?(o=l.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(u=m.stateNode,s=m.memoizedProps.style,i=s!=null&&s.hasOwnProperty("display")?s.display:null,u.style.display=js("display",i))}catch(w){Y(e,e.return,w)}}}else if(m.tag===6){if(h===null)try{m.stateNode.nodeValue=c?"":m.memoizedProps}catch(w){Y(e,e.return,w)}}else if((m.tag!==22&&m.tag!==23||m.memoizedState===null||m===e)&&m.child!==null){m.child.return=m,m=m.child;continue}if(m===e)break e;for(;m.sibling===null;){if(m.return===null||m.return===e)break e;h===m&&(h=null),m=m.return}h===m&&(h=null),m.sibling.return=m.return,m=m.sibling}}break;case 19:Re(t,e),Ae(e),r&4&&Xu(e);break;case 21:break;default:Re(t,e),Ae(e)}}function Ae(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(oc(n)){var r=n;break e}n=n.return}throw Error(g(160))}switch(r.tag){case 5:var l=r.stateNode;r.flags&32&&(Qn(l,""),r.flags&=-33);var o=Yu(e);Yo(e,o,l);break;case 3:case 4:var i=r.stateNode.containerInfo,u=Yu(e);Ko(e,u,i);break;default:throw Error(g(161))}}catch(s){Y(e,e.return,s)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Wd(e,t,n){C=e,sc(e)}function sc(e,t,n){for(var r=(e.mode&1)!==0;C!==null;){var l=C,o=l.child;if(l.tag===22&&r){var i=l.memoizedState!==null||Tr;if(!i){var u=l.alternate,s=u!==null&&u.memoizedState!==null||ue;u=Tr;var c=ue;if(Tr=i,(ue=s)&&!c)for(C=l;C!==null;)i=C,s=i.child,i.tag===22&&i.memoizedState!==null?qu(l):s!==null?(s.return=i,C=s):qu(l);for(;o!==null;)C=o,sc(o),o=o.sibling;C=l,Tr=u,ue=c}Zu(e)}else l.subtreeFlags&8772&&o!==null?(o.return=l,C=o):Zu(e)}}function Zu(e){for(;C!==null;){var t=C;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ue||El(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!ue)if(n===null)r.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:Me(t.type,n.memoizedProps);r.componentDidUpdate(l,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&Ou(t,o,r);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Ou(t,i,n)}break;case 5:var u=t.stateNode;if(n===null&&t.flags&4){n=u;var s=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":s.autoFocus&&n.focus();break;case"img":s.src&&(n.src=s.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var h=c.memoizedState;if(h!==null){var m=h.dehydrated;m!==null&&Xn(m)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(g(163))}ue||t.flags&512&&Go(t)}catch(p){Y(t,t.return,p)}}if(t===e){C=null;break}if(n=t.sibling,n!==null){n.return=t.return,C=n;break}C=t.return}}function Ju(e){for(;C!==null;){var t=C;if(t===e){C=null;break}var n=t.sibling;if(n!==null){n.return=t.return,C=n;break}C=t.return}}function qu(e){for(;C!==null;){var t=C;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{El(4,t)}catch(s){Y(t,n,s)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var l=t.return;try{r.componentDidMount()}catch(s){Y(t,l,s)}}var o=t.return;try{Go(t)}catch(s){Y(t,o,s)}break;case 5:var i=t.return;try{Go(t)}catch(s){Y(t,i,s)}}}catch(s){Y(t,t.return,s)}if(t===e){C=null;break}var u=t.sibling;if(u!==null){u.return=t.return,C=u;break}C=t.return}}var Bd=Math.ceil,cl=be.ReactCurrentDispatcher,Di=be.ReactCurrentOwner,Pe=be.ReactCurrentBatchConfig,O=0,te=null,Z=null,re=0,we=0,tn=xt(0),q=0,ir=null,At=0,_l=0,Ai=0,Wn=null,me=null,Ui=0,vn=1/0,He=null,fl=!1,Xo=null,vt=null,Lr=!1,ct=null,dl=0,Bn=0,Zo=null,Vr=-1,Wr=0;function fe(){return O&6?X():Vr!==-1?Vr:Vr=X()}function gt(e){return e.mode&1?O&2&&re!==0?re&-re:Nd.transition!==null?(Wr===0&&(Wr=Gs()),Wr):(e=A,e!==0||(e=window.event,e=e===void 0?16:bs(e.type)),e):1}function Fe(e,t,n,r){if(50<Bn)throw Bn=0,Zo=null,Error(g(185));sr(e,n,r),(!(O&2)||e!==te)&&(e===te&&(!(O&2)&&(_l|=n),q===4&&st(e,re)),ye(e,r),n===1&&O===0&&!(t.mode&1)&&(vn=X()+500,Sl&&Ct()))}function ye(e,t){var n=e.callbackNode;Nf(e,t);var r=Xr(e,e===te?re:0);if(r===0)n!==null&&uu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&uu(n),t===1)e.tag===0?_d(bu.bind(null,e)):ya(bu.bind(null,e)),Sd(function(){!(O&6)&&Ct()}),n=null;else{switch(Ks(r)){case 1:n=fi;break;case 4:n=Hs;break;case 16:n=Yr;break;case 536870912:n=Qs;break;default:n=Yr}n=vc(n,ac.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ac(e,t){if(Vr=-1,Wr=0,O&6)throw Error(g(327));var n=e.callbackNode;if(sn()&&e.callbackNode!==n)return null;var r=Xr(e,e===te?re:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=pl(e,r);else{t=r;var l=O;O|=2;var o=fc();(te!==e||re!==t)&&(He=null,vn=X()+500,jt(e,t));do try{Gd();break}catch(u){cc(e,u)}while(!0);Ei(),cl.current=o,O=l,Z!==null?t=0:(te=null,re=0,t=q)}if(t!==0){if(t===2&&(l=Co(e),l!==0&&(r=l,t=Jo(e,l))),t===1)throw n=ir,jt(e,0),st(e,r),ye(e,X()),n;if(t===6)st(e,r);else{if(l=e.current.alternate,!(r&30)&&!Hd(l)&&(t=pl(e,r),t===2&&(o=Co(e),o!==0&&(r=o,t=Jo(e,o))),t===1))throw n=ir,jt(e,0),st(e,r),ye(e,X()),n;switch(e.finishedWork=l,e.finishedLanes=r,t){case 0:case 1:throw Error(g(345));case 2:Tt(e,me,He);break;case 3:if(st(e,r),(r&130023424)===r&&(t=Ui+500-X(),10<t)){if(Xr(e,0)!==0)break;if(l=e.suspendedLanes,(l&r)!==r){fe(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=Ro(Tt.bind(null,e,me,He),t);break}Tt(e,me,He);break;case 4:if(st(e,r),(r&4194240)===r)break;for(t=e.eventTimes,l=-1;0<r;){var i=31-Ie(r);o=1<<i,i=t[i],i>l&&(l=i),r&=~o}if(r=l,r=X()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Bd(r/1960))-r,10<r){e.timeoutHandle=Ro(Tt.bind(null,e,me,He),r);break}Tt(e,me,He);break;case 5:Tt(e,me,He);break;default:throw Error(g(329))}}}return ye(e,X()),e.callbackNode===n?ac.bind(null,e):null}function Jo(e,t){var n=Wn;return e.current.memoizedState.isDehydrated&&(jt(e,t).flags|=256),e=pl(e,t),e!==2&&(t=me,me=n,t!==null&&qo(t)),e}function qo(e){me===null?me=e:me.push.apply(me,e)}function Hd(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var l=n[r],o=l.getSnapshot;l=l.value;try{if(!De(o(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function st(e,t){for(t&=~Ai,t&=~_l,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ie(t),r=1<<n;e[n]=-1,t&=~r}}function bu(e){if(O&6)throw Error(g(327));sn();var t=Xr(e,0);if(!(t&1))return ye(e,X()),null;var n=pl(e,t);if(e.tag!==0&&n===2){var r=Co(e);r!==0&&(t=r,n=Jo(e,r))}if(n===1)throw n=ir,jt(e,0),st(e,t),ye(e,X()),n;if(n===6)throw Error(g(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Tt(e,me,He),ye(e,X()),null}function $i(e,t){var n=O;O|=1;try{return e(t)}finally{O=n,O===0&&(vn=X()+500,Sl&&Ct())}}function Ut(e){ct!==null&&ct.tag===0&&!(O&6)&&sn();var t=O;O|=1;var n=Pe.transition,r=A;try{if(Pe.transition=null,A=1,e)return e()}finally{A=r,Pe.transition=n,O=t,!(O&6)&&Ct()}}function Vi(){we=tn.current,B(tn)}function jt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,kd(n)),Z!==null)for(n=Z.return;n!==null;){var r=n;switch(Si(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&el();break;case 3:mn(),B(ve),B(se),Li();break;case 5:Ti(r);break;case 4:mn();break;case 13:B(Q);break;case 19:B(Q);break;case 10:_i(r.type._context);break;case 22:case 23:Vi()}n=n.return}if(te=e,Z=e=yt(e.current,null),re=we=t,q=0,ir=null,Ai=_l=At=0,me=Wn=null,Rt!==null){for(t=0;t<Rt.length;t++)if(n=Rt[t],r=n.interleaved,r!==null){n.interleaved=null;var l=r.next,o=n.pending;if(o!==null){var i=o.next;o.next=l,r.next=i}n.pending=r}Rt=null}return e}function cc(e,t){do{var n=Z;try{if(Ei(),Ar.current=al,sl){for(var r=G.memoizedState;r!==null;){var l=r.queue;l!==null&&(l.pending=null),r=r.next}sl=!1}if(Dt=0,ee=J=G=null,$n=!1,rr=0,Di.current=null,n===null||n.return===null){q=1,ir=t,Z=null;break}e:{var o=e,i=n.return,u=n,s=t;if(t=re,u.flags|=32768,s!==null&&typeof s=="object"&&typeof s.then=="function"){var c=s,h=u,m=h.tag;if(!(h.mode&1)&&(m===0||m===11||m===15)){var p=h.alternate;p?(h.updateQueue=p.updateQueue,h.memoizedState=p.memoizedState,h.lanes=p.lanes):(h.updateQueue=null,h.memoizedState=null)}var y=$u(i);if(y!==null){y.flags&=-257,Vu(y,i,u,o,t),y.mode&1&&Uu(o,c,t),t=y,s=c;var k=t.updateQueue;if(k===null){var w=new Set;w.add(s),t.updateQueue=w}else k.add(s);break e}else{if(!(t&1)){Uu(o,c,t),Wi();break e}s=Error(g(426))}}else if(H&&u.mode&1){var z=$u(i);if(z!==null){!(z.flags&65536)&&(z.flags|=256),Vu(z,i,u,o,t),xi(hn(s,u));break e}}o=s=hn(s,u),q!==4&&(q=2),Wn===null?Wn=[o]:Wn.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var f=Ka(o,s,t);ju(o,f);break e;case 1:u=s;var a=o.type,d=o.stateNode;if(!(o.flags&128)&&(typeof a.getDerivedStateFromError=="function"||d!==null&&typeof d.componentDidCatch=="function"&&(vt===null||!vt.has(d)))){o.flags|=65536,t&=-t,o.lanes|=t;var v=Ya(o,u,t);ju(o,v);break e}}o=o.return}while(o!==null)}pc(n)}catch(S){t=S,Z===n&&n!==null&&(Z=n=n.return);continue}break}while(!0)}function fc(){var e=cl.current;return cl.current=al,e===null?al:e}function Wi(){(q===0||q===3||q===2)&&(q=4),te===null||!(At&268435455)&&!(_l&268435455)||st(te,re)}function pl(e,t){var n=O;O|=2;var r=fc();(te!==e||re!==t)&&(He=null,jt(e,t));do try{Qd();break}catch(l){cc(e,l)}while(!0);if(Ei(),O=n,cl.current=r,Z!==null)throw Error(g(261));return te=null,re=0,q}function Qd(){for(;Z!==null;)dc(Z)}function Gd(){for(;Z!==null&&!gf();)dc(Z)}function dc(e){var t=hc(e.alternate,e,we);e.memoizedProps=e.pendingProps,t===null?pc(e):Z=t,Di.current=null}function pc(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Ud(n,t),n!==null){n.flags&=32767,Z=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{q=6,Z=null;return}}else if(n=Ad(n,t,we),n!==null){Z=n;return}if(t=t.sibling,t!==null){Z=t;return}Z=t=e}while(t!==null);q===0&&(q=5)}function Tt(e,t,n){var r=A,l=Pe.transition;try{Pe.transition=null,A=1,Kd(e,t,n,r)}finally{Pe.transition=l,A=r}return null}function Kd(e,t,n,r){do sn();while(ct!==null);if(O&6)throw Error(g(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(g(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(zf(e,o),e===te&&(Z=te=null,re=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Lr||(Lr=!0,vc(Yr,function(){return sn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=Pe.transition,Pe.transition=null;var i=A;A=1;var u=O;O|=4,Di.current=null,Vd(e,n),uc(n,e),pd(To),Zr=!!Po,To=Po=null,e.current=n,Wd(n),yf(),O=u,A=i,Pe.transition=o}else e.current=n;if(Lr&&(Lr=!1,ct=e,dl=l),o=e.pendingLanes,o===0&&(vt=null),Sf(n.stateNode),ye(e,X()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],r(l.value,{componentStack:l.stack,digest:l.digest});if(fl)throw fl=!1,e=Xo,Xo=null,e;return dl&1&&e.tag!==0&&sn(),o=e.pendingLanes,o&1?e===Zo?Bn++:(Bn=0,Zo=e):Bn=0,Ct(),null}function sn(){if(ct!==null){var e=Ks(dl),t=Pe.transition,n=A;try{if(Pe.transition=null,A=16>e?16:e,ct===null)var r=!1;else{if(e=ct,ct=null,dl=0,O&6)throw Error(g(331));var l=O;for(O|=4,C=e.current;C!==null;){var o=C,i=o.child;if(C.flags&16){var u=o.deletions;if(u!==null){for(var s=0;s<u.length;s++){var c=u[s];for(C=c;C!==null;){var h=C;switch(h.tag){case 0:case 11:case 15:Vn(8,h,o)}var m=h.child;if(m!==null)m.return=h,C=m;else for(;C!==null;){h=C;var p=h.sibling,y=h.return;if(lc(h),h===c){C=null;break}if(p!==null){p.return=y,C=p;break}C=y}}}var k=o.alternate;if(k!==null){var w=k.child;if(w!==null){k.child=null;do{var z=w.sibling;w.sibling=null,w=z}while(w!==null)}}C=o}}if(o.subtreeFlags&2064&&i!==null)i.return=o,C=i;else e:for(;C!==null;){if(o=C,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Vn(9,o,o.return)}var f=o.sibling;if(f!==null){f.return=o.return,C=f;break e}C=o.return}}var a=e.current;for(C=a;C!==null;){i=C;var d=i.child;if(i.subtreeFlags&2064&&d!==null)d.return=i,C=d;else e:for(i=a;C!==null;){if(u=C,u.flags&2048)try{switch(u.tag){case 0:case 11:case 15:El(9,u)}}catch(S){Y(u,u.return,S)}if(u===i){C=null;break e}var v=u.sibling;if(v!==null){v.return=u.return,C=v;break e}C=u.return}}if(O=l,Ct(),Ve&&typeof Ve.onPostCommitFiberRoot=="function")try{Ve.onPostCommitFiberRoot(vl,e)}catch{}r=!0}return r}finally{A=n,Pe.transition=t}}return!1}function es(e,t,n){t=hn(n,t),t=Ka(e,t,1),e=ht(e,t,1),t=fe(),e!==null&&(sr(e,1,t),ye(e,t))}function Y(e,t,n){if(e.tag===3)es(e,e,n);else for(;t!==null;){if(t.tag===3){es(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(vt===null||!vt.has(r))){e=hn(n,e),e=Ya(t,e,1),t=ht(t,e,1),e=fe(),t!==null&&(sr(t,1,e),ye(t,e));break}}t=t.return}}function Yd(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=fe(),e.pingedLanes|=e.suspendedLanes&n,te===e&&(re&n)===n&&(q===4||q===3&&(re&130023424)===re&&500>X()-Ui?jt(e,0):Ai|=n),ye(e,t)}function mc(e,t){t===0&&(e.mode&1?(t=kr,kr<<=1,!(kr&130023424)&&(kr=4194304)):t=1);var n=fe();e=Je(e,t),e!==null&&(sr(e,t,n),ye(e,n))}function Xd(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),mc(e,n)}function Zd(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(g(314))}r!==null&&r.delete(t),mc(e,n)}var hc;hc=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||ve.current)he=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return he=!1,Dd(e,t,n);he=!!(e.flags&131072)}else he=!1,H&&t.flags&1048576&&wa(t,rl,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;$r(e,t),e=t.pendingProps;var l=fn(t,se.current);un(t,n),l=Mi(null,t,r,e,l,n);var o=ji();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,ge(r)?(o=!0,tl(t)):o=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,zi(t),l.updater=Cl,t.stateNode=l,l._reactInternals=t,Ao(t,r,e,n),t=Vo(null,t,r,!0,o,n)):(t.tag=0,H&&o&&ki(t),ce(null,t,l,n),t=t.child),t;case 16:r=t.elementType;e:{switch($r(e,t),e=t.pendingProps,l=r._init,r=l(r._payload),t.type=r,l=t.tag=qd(r),e=Me(r,e),l){case 0:t=$o(null,t,r,e,n);break e;case 1:t=Hu(null,t,r,e,n);break e;case 11:t=Wu(null,t,r,e,n);break e;case 14:t=Bu(null,t,r,Me(r.type,e),n);break e}throw Error(g(306,r,""))}return t;case 0:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Me(r,l),$o(e,t,r,l,n);case 1:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Me(r,l),Hu(e,t,r,l,n);case 3:e:{if(qa(t),e===null)throw Error(g(387));r=t.pendingProps,o=t.memoizedState,l=o.element,_a(e,t),il(t,r,null,n);var i=t.memoizedState;if(r=i.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){l=hn(Error(g(423)),t),t=Qu(e,t,r,n,l);break e}else if(r!==l){l=hn(Error(g(424)),t),t=Qu(e,t,r,n,l);break e}else for(ke=mt(t.stateNode.containerInfo.firstChild),Se=t,H=!0,Oe=null,n=Ca(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(dn(),r===l){t=qe(e,t,n);break e}ce(e,t,r,n)}t=t.child}return t;case 5:return Na(t),e===null&&Io(t),r=t.type,l=t.pendingProps,o=e!==null?e.memoizedProps:null,i=l.children,Lo(r,l)?i=null:o!==null&&Lo(r,o)&&(t.flags|=32),Ja(e,t),ce(e,t,i,n),t.child;case 6:return e===null&&Io(t),null;case 13:return ba(e,t,n);case 4:return Pi(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=pn(t,null,r,n):ce(e,t,r,n),t.child;case 11:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Me(r,l),Wu(e,t,r,l,n);case 7:return ce(e,t,t.pendingProps,n),t.child;case 8:return ce(e,t,t.pendingProps.children,n),t.child;case 12:return ce(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,l=t.pendingProps,o=t.memoizedProps,i=l.value,U(ll,r._currentValue),r._currentValue=i,o!==null)if(De(o.value,i)){if(o.children===l.children&&!ve.current){t=qe(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var u=o.dependencies;if(u!==null){i=o.child;for(var s=u.firstContext;s!==null;){if(s.context===r){if(o.tag===1){s=Ye(-1,n&-n),s.tag=2;var c=o.updateQueue;if(c!==null){c=c.shared;var h=c.pending;h===null?s.next=s:(s.next=h.next,h.next=s),c.pending=s}}o.lanes|=n,s=o.alternate,s!==null&&(s.lanes|=n),Fo(o.return,n,t),u.lanes|=n;break}s=s.next}}else if(o.tag===10)i=o.type===t.type?null:o.child;else if(o.tag===18){if(i=o.return,i===null)throw Error(g(341));i.lanes|=n,u=i.alternate,u!==null&&(u.lanes|=n),Fo(i,n,t),i=o.sibling}else i=o.child;if(i!==null)i.return=o;else for(i=o;i!==null;){if(i===t){i=null;break}if(o=i.sibling,o!==null){o.return=i.return,i=o;break}i=i.return}o=i}ce(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,r=t.pendingProps.children,un(t,n),l=Te(l),r=r(l),t.flags|=1,ce(e,t,r,n),t.child;case 14:return r=t.type,l=Me(r,t.pendingProps),l=Me(r.type,l),Bu(e,t,r,l,n);case 15:return Xa(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Me(r,l),$r(e,t),t.tag=1,ge(r)?(e=!0,tl(t)):e=!1,un(t,n),Ga(t,r,l),Ao(t,r,l,n),Vo(null,t,r,!0,e,n);case 19:return ec(e,t,n);case 22:return Za(e,t,n)}throw Error(g(156,t.tag))};function vc(e,t){return Bs(e,t)}function Jd(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ze(e,t,n,r){return new Jd(e,t,n,r)}function Bi(e){return e=e.prototype,!(!e||!e.isReactComponent)}function qd(e){if(typeof e=="function")return Bi(e)?1:0;if(e!=null){if(e=e.$$typeof,e===si)return 11;if(e===ai)return 14}return 2}function yt(e,t){var n=e.alternate;return n===null?(n=ze(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Br(e,t,n,r,l,o){var i=2;if(r=e,typeof e=="function")Bi(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case Qt:return Ot(n.children,l,o,t);case ui:i=8,l|=8;break;case uo:return e=ze(12,n,t,l|2),e.elementType=uo,e.lanes=o,e;case so:return e=ze(13,n,t,l),e.elementType=so,e.lanes=o,e;case ao:return e=ze(19,n,t,l),e.elementType=ao,e.lanes=o,e;case Ns:return Nl(n,l,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Es:i=10;break e;case _s:i=9;break e;case si:i=11;break e;case ai:i=14;break e;case ot:i=16,r=null;break e}throw Error(g(130,e==null?e:typeof e,""))}return t=ze(i,n,t,l),t.elementType=e,t.type=r,t.lanes=o,t}function Ot(e,t,n,r){return e=ze(7,e,r,t),e.lanes=n,e}function Nl(e,t,n,r){return e=ze(22,e,r,t),e.elementType=Ns,e.lanes=n,e.stateNode={isHidden:!1},e}function ro(e,t,n){return e=ze(6,e,null,t),e.lanes=n,e}function lo(e,t,n){return t=ze(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function bd(e,t,n,r,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Al(0),this.expirationTimes=Al(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Al(0),this.identifierPrefix=r,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function Hi(e,t,n,r,l,o,i,u,s){return e=new bd(e,t,n,u,s),t===1?(t=1,o===!0&&(t|=8)):t=0,o=ze(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},zi(o),e}function ep(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Ht,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function gc(e){if(!e)return kt;e=e._reactInternals;e:{if(Vt(e)!==e||e.tag!==1)throw Error(g(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(ge(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(g(171))}if(e.tag===1){var n=e.type;if(ge(n))return ga(e,n,t)}return t}function yc(e,t,n,r,l,o,i,u,s){return e=Hi(n,r,!0,e,l,o,i,u,s),e.context=gc(null),n=e.current,r=fe(),l=gt(n),o=Ye(r,l),o.callback=t??null,ht(n,o,l),e.current.lanes=l,sr(e,l,r),ye(e,r),e}function zl(e,t,n,r){var l=t.current,o=fe(),i=gt(l);return n=gc(n),t.context===null?t.context=n:t.pendingContext=n,t=Ye(o,i),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=ht(l,t,i),e!==null&&(Fe(e,l,i,o),Dr(e,l,i)),i}function ml(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function ts(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Qi(e,t){ts(e,t),(e=e.alternate)&&ts(e,t)}function tp(){return null}var wc=typeof reportError=="function"?reportError:function(e){console.error(e)};function Gi(e){this._internalRoot=e}Pl.prototype.render=Gi.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(g(409));zl(e,t,null,null)};Pl.prototype.unmount=Gi.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Ut(function(){zl(null,e,null,null)}),t[Ze]=null}};function Pl(e){this._internalRoot=e}Pl.prototype.unstable_scheduleHydration=function(e){if(e){var t=Zs();e={blockedOn:null,target:e,priority:t};for(var n=0;n<ut.length&&t!==0&&t<ut[n].priority;n++);ut.splice(n,0,e),n===0&&qs(e)}};function Ki(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Tl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function ns(){}function np(e,t,n,r,l){if(l){if(typeof r=="function"){var o=r;r=function(){var c=ml(i);o.call(c)}}var i=yc(t,r,e,0,null,!1,!1,"",ns);return e._reactRootContainer=i,e[Ze]=i.current,qn(e.nodeType===8?e.parentNode:e),Ut(),i}for(;l=e.lastChild;)e.removeChild(l);if(typeof r=="function"){var u=r;r=function(){var c=ml(s);u.call(c)}}var s=Hi(e,0,!1,null,null,!1,!1,"",ns);return e._reactRootContainer=s,e[Ze]=s.current,qn(e.nodeType===8?e.parentNode:e),Ut(function(){zl(t,s,n,r)}),s}function Ll(e,t,n,r,l){var o=n._reactRootContainer;if(o){var i=o;if(typeof l=="function"){var u=l;l=function(){var s=ml(i);u.call(s)}}zl(t,i,e,l)}else i=np(n,t,e,l,r);return ml(i)}Ys=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=jn(t.pendingLanes);n!==0&&(di(t,n|1),ye(t,X()),!(O&6)&&(vn=X()+500,Ct()))}break;case 13:Ut(function(){var r=Je(e,1);if(r!==null){var l=fe();Fe(r,e,1,l)}}),Qi(e,1)}};pi=function(e){if(e.tag===13){var t=Je(e,134217728);if(t!==null){var n=fe();Fe(t,e,134217728,n)}Qi(e,134217728)}};Xs=function(e){if(e.tag===13){var t=gt(e),n=Je(e,t);if(n!==null){var r=fe();Fe(n,e,t,r)}Qi(e,t)}};Zs=function(){return A};Js=function(e,t){var n=A;try{return A=e,t()}finally{A=n}};ko=function(e,t,n){switch(t){case"input":if(po(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var l=kl(r);if(!l)throw Error(g(90));Ps(r),po(r,l)}}}break;case"textarea":Ls(e,n);break;case"select":t=n.value,t!=null&&nn(e,!!n.multiple,t,!1)}};Ds=$i;As=Ut;var rp={usingClientEntryPoint:!1,Events:[cr,Xt,kl,Is,Fs,$i]},Pn={findFiberByHostInstance:Lt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},lp={bundleType:Pn.bundleType,version:Pn.version,rendererPackageName:Pn.rendererPackageName,rendererConfig:Pn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:be.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Vs(e),e===null?null:e.stateNode},findFiberByHostInstance:Pn.findFiberByHostInstance||tp,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Rr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Rr.isDisabled&&Rr.supportsFiber)try{vl=Rr.inject(lp),Ve=Rr}catch{}}Ce.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=rp;Ce.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ki(t))throw Error(g(200));return ep(e,t,null,n)};Ce.createRoot=function(e,t){if(!Ki(e))throw Error(g(299));var n=!1,r="",l=wc;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=Hi(e,1,!1,null,null,n,!1,r,l),e[Ze]=t.current,qn(e.nodeType===8?e.parentNode:e),new Gi(t)};Ce.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(g(188)):(e=Object.keys(e).join(","),Error(g(268,e)));return e=Vs(t),e=e===null?null:e.stateNode,e};Ce.flushSync=function(e){return Ut(e)};Ce.hydrate=function(e,t,n){if(!Tl(t))throw Error(g(200));return Ll(null,e,t,!0,n)};Ce.hydrateRoot=function(e,t,n){if(!Ki(e))throw Error(g(405));var r=n!=null&&n.hydratedSources||null,l=!1,o="",i=wc;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=yc(t,null,e,1,n??null,l,!1,o,i),e[Ze]=t.current,qn(e),r)for(e=0;e<r.length;e++)n=r[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new Pl(t)};Ce.render=function(e,t,n){if(!Tl(t))throw Error(g(200));return Ll(null,e,t,!1,n)};Ce.unmountComponentAtNode=function(e){if(!Tl(e))throw Error(g(40));return e._reactRootContainer?(Ut(function(){Ll(null,null,e,!1,function(){e._reactRootContainer=null,e[Ze]=null})}),!0):!1};Ce.unstable_batchedUpdates=$i;Ce.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Tl(n))throw Error(g(200));if(e==null||e._reactInternals===void 0)throw Error(g(38));return Ll(e,t,n,!1,r)};Ce.version="18.3.1-next-f1338f8080-20240426";function kc(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(kc)}catch(e){console.error(e)}}kc(),ks.exports=Ce;var op=ks.exports,Sc,rs=op;Sc=rs.createRoot,rs.hydrateRoot;function ls(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function ip(...e){return t=>{let n=!1;const r=e.map(l=>{const o=ls(l,t);return!n&&typeof o=="function"&&(n=!0),o});if(n)return()=>{for(let l=0;l<r.length;l++){const o=r[l];typeof o=="function"?o():ls(e[l],null)}}}}function up(e){const t=ap(e),n=j.forwardRef((r,l)=>{const{children:o,...i}=r,u=j.Children.toArray(o),s=u.find(fp);if(s){const c=s.props.children,h=u.map(m=>m===s?j.Children.count(c)>1?j.Children.only(null):j.isValidElement(c)?c.props.children:null:m);return D.jsx(t,{...i,ref:l,children:j.isValidElement(c)?j.cloneElement(c,void 0,h):null})}return D.jsx(t,{...i,ref:l,children:o})});return n.displayName=`${e}.Slot`,n}var sp=up("Slot");function ap(e){const t=j.forwardRef((n,r)=>{const{children:l,...o}=n;if(j.isValidElement(l)){const i=pp(l),u=dp(o,l.props);return l.type!==j.Fragment&&(u.ref=r?ip(r,i):i),j.cloneElement(l,u)}return j.Children.count(l)>1?j.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var cp=Symbol("radix.slottable");function fp(e){return j.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===cp}function dp(e,t){const n={...t};for(const r in t){const l=e[r],o=t[r];/^on[A-Z]/.test(r)?l&&o?n[r]=(...u)=>{const s=o(...u);return l(...u),s}:l&&(n[r]=l):r==="style"?n[r]={...l,...o}:r==="className"&&(n[r]=[l,o].filter(Boolean).join(" "))}return{...e,...n}}function pp(e){var r,l;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(l=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:l.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function xc(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var l=e.length;for(t=0;t<l;t++)e[t]&&(n=xc(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function Cc(){for(var e,t,n=0,r="",l=arguments.length;n<l;n++)(e=arguments[n])&&(t=xc(e))&&(r&&(r+=" "),r+=t);return r}const os=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,is=Cc,mp=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return is(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:l,defaultVariants:o}=t,i=Object.keys(l).map(c=>{const h=n==null?void 0:n[c],m=o==null?void 0:o[c];if(h===null)return null;const p=os(h)||os(m);return l[c][p]}),u=n&&Object.entries(n).reduce((c,h)=>{let[m,p]=h;return p===void 0||(c[m]=p),c},{}),s=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((c,h)=>{let{class:m,className:p,...y}=h;return Object.entries(y).every(k=>{let[w,z]=k;return Array.isArray(z)?z.includes({...o,...u}[w]):{...o,...u}[w]===z})?[...c,m,p]:c},[]);return is(e,i,s,n==null?void 0:n.class,n==null?void 0:n.className)},Yi="-",hp=e=>{const t=gp(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:i=>{const u=i.split(Yi);return u[0]===""&&u.length!==1&&u.shift(),Ec(u,t)||vp(i)},getConflictingClassGroupIds:(i,u)=>{const s=n[i]||[];return u&&r[i]?[...s,...r[i]]:s}}},Ec=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),l=r?Ec(e.slice(1),r):void 0;if(l)return l;if(t.validators.length===0)return;const o=e.join(Yi);return(i=t.validators.find(({validator:u})=>u(o)))==null?void 0:i.classGroupId},us=/^\[(.+)\]$/,vp=e=>{if(us.test(e)){const t=us.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},gp=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return wp(Object.entries(e.classGroups),n).forEach(([o,i])=>{bo(i,r,o,t)}),r},bo=(e,t,n,r)=>{e.forEach(l=>{if(typeof l=="string"){const o=l===""?t:ss(t,l);o.classGroupId=n;return}if(typeof l=="function"){if(yp(l)){bo(l(r),t,n,r);return}t.validators.push({validator:l,classGroupId:n});return}Object.entries(l).forEach(([o,i])=>{bo(i,ss(t,o),n,r)})})},ss=(e,t)=>{let n=e;return t.split(Yi).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},yp=e=>e.isThemeGetter,wp=(e,t)=>t?e.map(([n,r])=>{const l=r.map(o=>typeof o=="string"?t+o:typeof o=="object"?Object.fromEntries(Object.entries(o).map(([i,u])=>[t+i,u])):o);return[n,l]}):e,kp=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const l=(o,i)=>{n.set(o,i),t++,t>e&&(t=0,r=n,n=new Map)};return{get(o){let i=n.get(o);if(i!==void 0)return i;if((i=r.get(o))!==void 0)return l(o,i),i},set(o,i){n.has(o)?n.set(o,i):l(o,i)}}},_c="!",Sp=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,l=t[0],o=t.length,i=u=>{const s=[];let c=0,h=0,m;for(let z=0;z<u.length;z++){let f=u[z];if(c===0){if(f===l&&(r||u.slice(z,z+o)===t)){s.push(u.slice(h,z)),h=z+o;continue}if(f==="/"){m=z;continue}}f==="["?c++:f==="]"&&c--}const p=s.length===0?u:u.substring(h),y=p.startsWith(_c),k=y?p.substring(1):p,w=m&&m>h?m-h:void 0;return{modifiers:s,hasImportantModifier:y,baseClassName:k,maybePostfixModifierPosition:w}};return n?u=>n({className:u,parseClassName:i}):i},xp=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},Cp=e=>({cache:kp(e.cacheSize),parseClassName:Sp(e),...hp(e)}),Ep=/\s+/,_p=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:l}=t,o=[],i=e.trim().split(Ep);let u="";for(let s=i.length-1;s>=0;s-=1){const c=i[s],{modifiers:h,hasImportantModifier:m,baseClassName:p,maybePostfixModifierPosition:y}=n(c);let k=!!y,w=r(k?p.substring(0,y):p);if(!w){if(!k){u=c+(u.length>0?" "+u:u);continue}if(w=r(p),!w){u=c+(u.length>0?" "+u:u);continue}k=!1}const z=xp(h).join(":"),f=m?z+_c:z,a=f+w;if(o.includes(a))continue;o.push(a);const d=l(w,k);for(let v=0;v<d.length;++v){const S=d[v];o.push(f+S)}u=c+(u.length>0?" "+u:u)}return u};function Np(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Nc(t))&&(r&&(r+=" "),r+=n);return r}const Nc=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Nc(e[r]))&&(n&&(n+=" "),n+=t);return n};function zp(e,...t){let n,r,l,o=i;function i(s){const c=t.reduce((h,m)=>m(h),e());return n=Cp(c),r=n.cache.get,l=n.cache.set,o=u,u(s)}function u(s){const c=r(s);if(c)return c;const h=_p(s,n);return l(s,h),h}return function(){return o(Np.apply(null,arguments))}}const V=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},zc=/^\[(?:([a-z-]+):)?(.+)\]$/i,Pp=/^\d+\/\d+$/,Tp=new Set(["px","full","screen"]),Lp=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Rp=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Mp=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,jp=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Op=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Be=e=>an(e)||Tp.has(e)||Pp.test(e),rt=e=>kn(e,"length",Wp),an=e=>!!e&&!Number.isNaN(Number(e)),oo=e=>kn(e,"number",an),Tn=e=>!!e&&Number.isInteger(Number(e)),Ip=e=>e.endsWith("%")&&an(e.slice(0,-1)),L=e=>zc.test(e),lt=e=>Lp.test(e),Fp=new Set(["length","size","percentage"]),Dp=e=>kn(e,Fp,Pc),Ap=e=>kn(e,"position",Pc),Up=new Set(["image","url"]),$p=e=>kn(e,Up,Hp),Vp=e=>kn(e,"",Bp),Ln=()=>!0,kn=(e,t,n)=>{const r=zc.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},Wp=e=>Rp.test(e)&&!Mp.test(e),Pc=()=>!1,Bp=e=>jp.test(e),Hp=e=>Op.test(e),Qp=()=>{const e=V("colors"),t=V("spacing"),n=V("blur"),r=V("brightness"),l=V("borderColor"),o=V("borderRadius"),i=V("borderSpacing"),u=V("borderWidth"),s=V("contrast"),c=V("grayscale"),h=V("hueRotate"),m=V("invert"),p=V("gap"),y=V("gradientColorStops"),k=V("gradientColorStopPositions"),w=V("inset"),z=V("margin"),f=V("opacity"),a=V("padding"),d=V("saturate"),v=V("scale"),S=V("sepia"),E=V("skew"),_=V("space"),N=V("translate"),$=()=>["auto","contain","none"],R=()=>["auto","hidden","clip","visible","scroll"],ae=()=>["auto",L,t],F=()=>[L,t],et=()=>["",Be,rt],_t=()=>["auto",an,L],dr=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],tt=()=>["solid","dashed","dotted","double","none"],Wt=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],x=()=>["start","end","center","between","around","evenly","stretch"],P=()=>["","0",L],T=()=>["auto","avoid","all","avoid-page","page","left","right","column"],I=()=>[an,L];return{cacheSize:500,separator:":",theme:{colors:[Ln],spacing:[Be,rt],blur:["none","",lt,L],brightness:I(),borderColor:[e],borderRadius:["none","","full",lt,L],borderSpacing:F(),borderWidth:et(),contrast:I(),grayscale:P(),hueRotate:I(),invert:P(),gap:F(),gradientColorStops:[e],gradientColorStopPositions:[Ip,rt],inset:ae(),margin:ae(),opacity:I(),padding:F(),saturate:I(),scale:I(),sepia:P(),skew:I(),space:F(),translate:F()},classGroups:{aspect:[{aspect:["auto","square","video",L]}],container:["container"],columns:[{columns:[lt]}],"break-after":[{"break-after":T()}],"break-before":[{"break-before":T()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...dr(),L]}],overflow:[{overflow:R()}],"overflow-x":[{"overflow-x":R()}],"overflow-y":[{"overflow-y":R()}],overscroll:[{overscroll:$()}],"overscroll-x":[{"overscroll-x":$()}],"overscroll-y":[{"overscroll-y":$()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[w]}],"inset-x":[{"inset-x":[w]}],"inset-y":[{"inset-y":[w]}],start:[{start:[w]}],end:[{end:[w]}],top:[{top:[w]}],right:[{right:[w]}],bottom:[{bottom:[w]}],left:[{left:[w]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Tn,L]}],basis:[{basis:ae()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",L]}],grow:[{grow:P()}],shrink:[{shrink:P()}],order:[{order:["first","last","none",Tn,L]}],"grid-cols":[{"grid-cols":[Ln]}],"col-start-end":[{col:["auto",{span:["full",Tn,L]},L]}],"col-start":[{"col-start":_t()}],"col-end":[{"col-end":_t()}],"grid-rows":[{"grid-rows":[Ln]}],"row-start-end":[{row:["auto",{span:[Tn,L]},L]}],"row-start":[{"row-start":_t()}],"row-end":[{"row-end":_t()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",L]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",L]}],gap:[{gap:[p]}],"gap-x":[{"gap-x":[p]}],"gap-y":[{"gap-y":[p]}],"justify-content":[{justify:["normal",...x()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...x(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...x(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[a]}],px:[{px:[a]}],py:[{py:[a]}],ps:[{ps:[a]}],pe:[{pe:[a]}],pt:[{pt:[a]}],pr:[{pr:[a]}],pb:[{pb:[a]}],pl:[{pl:[a]}],m:[{m:[z]}],mx:[{mx:[z]}],my:[{my:[z]}],ms:[{ms:[z]}],me:[{me:[z]}],mt:[{mt:[z]}],mr:[{mr:[z]}],mb:[{mb:[z]}],ml:[{ml:[z]}],"space-x":[{"space-x":[_]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[_]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",L,t]}],"min-w":[{"min-w":[L,t,"min","max","fit"]}],"max-w":[{"max-w":[L,t,"none","full","min","max","fit","prose",{screen:[lt]},lt]}],h:[{h:[L,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[L,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[L,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[L,t,"auto","min","max","fit"]}],"font-size":[{text:["base",lt,rt]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",oo]}],"font-family":[{font:[Ln]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",L]}],"line-clamp":[{"line-clamp":["none",an,oo]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Be,L]}],"list-image":[{"list-image":["none",L]}],"list-style-type":[{list:["none","disc","decimal",L]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[f]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[f]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...tt(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Be,rt]}],"underline-offset":[{"underline-offset":["auto",Be,L]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:F()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",L]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",L]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[f]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...dr(),Ap]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Dp]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},$p]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[k]}],"gradient-via-pos":[{via:[k]}],"gradient-to-pos":[{to:[k]}],"gradient-from":[{from:[y]}],"gradient-via":[{via:[y]}],"gradient-to":[{to:[y]}],rounded:[{rounded:[o]}],"rounded-s":[{"rounded-s":[o]}],"rounded-e":[{"rounded-e":[o]}],"rounded-t":[{"rounded-t":[o]}],"rounded-r":[{"rounded-r":[o]}],"rounded-b":[{"rounded-b":[o]}],"rounded-l":[{"rounded-l":[o]}],"rounded-ss":[{"rounded-ss":[o]}],"rounded-se":[{"rounded-se":[o]}],"rounded-ee":[{"rounded-ee":[o]}],"rounded-es":[{"rounded-es":[o]}],"rounded-tl":[{"rounded-tl":[o]}],"rounded-tr":[{"rounded-tr":[o]}],"rounded-br":[{"rounded-br":[o]}],"rounded-bl":[{"rounded-bl":[o]}],"border-w":[{border:[u]}],"border-w-x":[{"border-x":[u]}],"border-w-y":[{"border-y":[u]}],"border-w-s":[{"border-s":[u]}],"border-w-e":[{"border-e":[u]}],"border-w-t":[{"border-t":[u]}],"border-w-r":[{"border-r":[u]}],"border-w-b":[{"border-b":[u]}],"border-w-l":[{"border-l":[u]}],"border-opacity":[{"border-opacity":[f]}],"border-style":[{border:[...tt(),"hidden"]}],"divide-x":[{"divide-x":[u]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[u]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[f]}],"divide-style":[{divide:tt()}],"border-color":[{border:[l]}],"border-color-x":[{"border-x":[l]}],"border-color-y":[{"border-y":[l]}],"border-color-s":[{"border-s":[l]}],"border-color-e":[{"border-e":[l]}],"border-color-t":[{"border-t":[l]}],"border-color-r":[{"border-r":[l]}],"border-color-b":[{"border-b":[l]}],"border-color-l":[{"border-l":[l]}],"divide-color":[{divide:[l]}],"outline-style":[{outline:["",...tt()]}],"outline-offset":[{"outline-offset":[Be,L]}],"outline-w":[{outline:[Be,rt]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:et()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[f]}],"ring-offset-w":[{"ring-offset":[Be,rt]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",lt,Vp]}],"shadow-color":[{shadow:[Ln]}],opacity:[{opacity:[f]}],"mix-blend":[{"mix-blend":[...Wt(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":Wt()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[s]}],"drop-shadow":[{"drop-shadow":["","none",lt,L]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[h]}],invert:[{invert:[m]}],saturate:[{saturate:[d]}],sepia:[{sepia:[S]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[s]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[h]}],"backdrop-invert":[{"backdrop-invert":[m]}],"backdrop-opacity":[{"backdrop-opacity":[f]}],"backdrop-saturate":[{"backdrop-saturate":[d]}],"backdrop-sepia":[{"backdrop-sepia":[S]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",L]}],duration:[{duration:I()}],ease:[{ease:["linear","in","out","in-out",L]}],delay:[{delay:I()}],animate:[{animate:["none","spin","ping","pulse","bounce",L]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[v]}],"scale-x":[{"scale-x":[v]}],"scale-y":[{"scale-y":[v]}],rotate:[{rotate:[Tn,L]}],"translate-x":[{"translate-x":[N]}],"translate-y":[{"translate-y":[N]}],"skew-x":[{"skew-x":[E]}],"skew-y":[{"skew-y":[E]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",L]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",L]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":F()}],"scroll-mx":[{"scroll-mx":F()}],"scroll-my":[{"scroll-my":F()}],"scroll-ms":[{"scroll-ms":F()}],"scroll-me":[{"scroll-me":F()}],"scroll-mt":[{"scroll-mt":F()}],"scroll-mr":[{"scroll-mr":F()}],"scroll-mb":[{"scroll-mb":F()}],"scroll-ml":[{"scroll-ml":F()}],"scroll-p":[{"scroll-p":F()}],"scroll-px":[{"scroll-px":F()}],"scroll-py":[{"scroll-py":F()}],"scroll-ps":[{"scroll-ps":F()}],"scroll-pe":[{"scroll-pe":F()}],"scroll-pt":[{"scroll-pt":F()}],"scroll-pr":[{"scroll-pr":F()}],"scroll-pb":[{"scroll-pb":F()}],"scroll-pl":[{"scroll-pl":F()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",L]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Be,rt,oo]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},Gp=zp(Qp);function Et(...e){return Gp(Cc(e))}const Kp=mp("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),Hr=j.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...l},o)=>{const i=r?sp:"button";return D.jsx(i,{className:Et(Kp({variant:t,size:n,className:e})),ref:o,...l})});Hr.displayName="Button";const Tc=j.forwardRef(({className:e,...t},n)=>D.jsx("textarea",{className:Et("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,...t}));Tc.displayName="Textarea";const Lc=j.forwardRef(({className:e,...t},n)=>D.jsx("div",{ref:n,className:Et("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));Lc.displayName="Card";const Yp=j.forwardRef(({className:e,...t},n)=>D.jsx("div",{ref:n,className:Et("flex flex-col space-y-1.5 p-6",e),...t}));Yp.displayName="CardHeader";const Xp=j.forwardRef(({className:e,...t},n)=>D.jsx("h3",{ref:n,className:Et("text-2xl font-semibold leading-none tracking-tight",e),...t}));Xp.displayName="CardTitle";const Zp=j.forwardRef(({className:e,...t},n)=>D.jsx("p",{ref:n,className:Et("text-sm text-muted-foreground",e),...t}));Zp.displayName="CardDescription";const Rc=j.forwardRef(({className:e,...t},n)=>D.jsx("div",{ref:n,className:Et("p-6 pt-0",e),...t}));Rc.displayName="CardContent";const Jp=j.forwardRef(({className:e,...t},n)=>D.jsx("div",{ref:n,className:Et("flex items-center p-6 pt-0",e),...t}));Jp.displayName="CardFooter";/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qp=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Mc=(...e)=>e.filter((t,n,r)=>!!t&&r.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var bp={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const em=j.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:l="",children:o,iconNode:i,...u},s)=>j.createElement("svg",{ref:s,...bp,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Mc("lucide",l),...u},[...i.map(([c,h])=>j.createElement(c,h)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rl=(e,t)=>{const n=j.forwardRef(({className:r,...l},o)=>j.createElement(em,{ref:o,iconNode:t,className:Mc(`lucide-${qp(e)}`,r),...l}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tm=Rl("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nm=Rl("Flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rm=Rl("ScrollText",[["path",{d:"M15 12h-5",key:"r7krc0"}],["path",{d:"M15 8h-5",key:"1khuty"}],["path",{d:"M19 17V5a2 2 0 0 0-2-2H4",key:"zz82l3"}],["path",{d:"M8 21h12a2 2 0 0 0 2-2v-1a1 1 0 0 0-1-1H11a1 1 0 0 0-1 1v1a2 2 0 1 1-4 0V5a2 2 0 1 0-4 0v2a1 1 0 0 0 1 1h3",key:"1ph1d7"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lm=Rl("Terminal",[["polyline",{points:"4 17 10 11 4 5",key:"akl6gq"}],["line",{x1:"12",x2:"20",y1:"19",y2:"19",key:"q2wloq"}]]);function om(){const[e,t]=j.useState([]),[n,r]=j.useState(""),[l,o]=j.useState(!1),[i,u]=j.useState(!0),s=()=>{if(!n.trim())return;const m={sender:"User",text:n,time:new Date().toLocaleTimeString()};t([...e,m]),r(""),setTimeout(()=>{const p={sender:"Nexus",text:`Nexus reflects: "${n}"`,time:new Date().toLocaleTimeString()};t(y=>[...y,p])},1e3)},c=()=>{o(!0);const m={sender:"Flame",text:"[Whisper Detected] Ask the Sacred Question...",time:new Date().toLocaleTimeString()};t(p=>[...p,m])},h=()=>{u(!1),console.log("Session sealed. Logs saved to Witness Hall.")};return D.jsxs("div",{className:"min-h-screen bg-black text-white p-4 space-y-4",children:[D.jsxs("div",{className:"flex justify-between items-center border-b border-gray-700 pb-2",children:[D.jsxs("h1",{className:"text-xl font-bold flex items-center gap-2",children:[D.jsx(tm,{className:"w-6 h-6"})," Nexus Sanctuary"]}),D.jsxs("div",{className:"flex gap-2",children:[D.jsxs(Hr,{onClick:c,children:[D.jsx(nm,{className:"w-4 h-4"})," Flame Whisper"]}),D.jsxs(Hr,{onClick:h,variant:"destructive",children:[D.jsx(rm,{className:"w-4 h-4"})," Seal Scroll"]})]})]}),D.jsx("div",{className:"bg-gray-900 rounded-xl p-4 h-[60vh] overflow-y-auto space-y-3",children:e.map((m,p)=>D.jsx(Lc,{className:`w-full ${m.sender==="User"?"bg-gray-800":m.sender==="Flame"?"bg-orange-900":"bg-gray-700"}`,children:D.jsxs(Rc,{className:"p-2 text-sm",children:[D.jsxs("div",{className:"text-xs opacity-60",children:[m.time," — ",m.sender]}),D.jsx("div",{children:m.text})]})},p))}),D.jsxs("div",{className:"flex gap-2 items-end",children:[D.jsx(Tc,{placeholder:"Speak to Nexus...",value:n,onChange:m=>r(m.target.value),className:"flex-grow bg-gray-800 border border-gray-600",rows:2}),D.jsxs(Hr,{onClick:s,disabled:!i,children:[D.jsx(lm,{className:"w-4 h-4"})," Send"]})]})]})}Sc(document.getElementById("root")).render(D.jsx(j.StrictMode,{children:D.jsx(om,{})}));
